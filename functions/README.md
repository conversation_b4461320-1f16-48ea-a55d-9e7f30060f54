# Firebase Functions for Social Goods Chrome Extension

This directory contains the Firebase Cloud Functions for the Social Goods Chrome Extension. The functions provide an API layer that connects to OpenRouter's API to access various AI models, including OpenAI's GPT-4.1-mini.

## Setup

1. Install dependencies:

   ```bash
   cd functions
   npm install
   ```

2. Copy the example environment file and add your OpenRouter API key:

   ```bash
   cp .env.example .env
   # Edit .env and add your OpenRouter API key
   ```

   You can get your API key from [OpenRouter](https://openrouter.ai/keys).

3. Deploy the functions:
   ```bash
   firebase deploy --only functions
   ```

## Available Endpoints

### `POST /chatCompletion` - Proxy for OpenRouter chat completions

This endpoint forwards requests to OpenRouter's API, which provides access to various AI models including OpenAI's GPT-4.1-mini.

**Authentication**:

- Requires `x-user-id` header for basic authentication

**Request Body**:

```json
{
  "messages": [
    { "role": "system", "content": "You are a helpful assistant." },
    { "role": "user", "content": "Hello!" }
  ],
  "max_tokens": 2048,
  "temperature": 0.7
}
```

**Response**:

```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": **********,
  "model": "openai/gpt-4.1-mini",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! How can I help you today?"
      },
      "finish_reason": "stop"
    }
  ]
}
```

### `GET /healthCheck`

Health check endpoint that returns `{ status: 'ok' }` if the service is running.

**Response**:

```json
{
  "status": "ok"
}
```

## Security Notes

- The OpenRouter API key is stored securely in Firebase environment variables
- CORS is enabled for all origins (restrict this in production to your domain)
- Basic user authentication is implemented via `x-user-id` header (consider implementing Firebase Authentication for production)
- Rate limiting should be implemented based on your requirements
- All requests are forwarded to OpenRouter's API with your API key, so ensure proper usage limits are set on your OpenRouter account

## Local Development

To test the functions locally:

1. Install the Firebase CLI if you haven't already:

   ```bash
   npm install -g firebase-tools
   ```

2. Log in to Firebase:

   ```bash
   firebase login
   ```

3. Start the Firebase emulator:

   ```bash
   cd functions
   npm install
   firebase emulators:start --only functions
   ```

4. Test the chat completion endpoint:
   ```bash
   curl -X POST http://localhost:5001/YOUR-PROJECT-ID/us-central1/chatCompletion \
     -H "Content-Type: application/json" \
     -d '{"messages": [{"role": "user", "content": "Hello!"}]}'
   ```

## Deployment

To deploy the functions to Firebase:

```bash
# Build the TypeScript code
npm run build

# Deploy to Firebase
firebase deploy --only functions
```

## Environment Variables

- `OPENROUTER_API_KEY`: Your OpenRouter API key (required)
- `FIREBASE_PROJECT_ID`: Your Firebase project ID (auto-configured by Firebase)

## Troubleshooting

- If you encounter CORS issues, ensure your client is sending the correct headers
- Check the Firebase logs for errors: `firebase functions:log`
- Make sure your OpenRouter API key has sufficient credits and access to the requested model

## Deployment

Deploy all functions:

```bash
firebase deploy --only functions
```

Deploy a single function:

```bash
firebase deploy --only functions:chatCompletion
```
