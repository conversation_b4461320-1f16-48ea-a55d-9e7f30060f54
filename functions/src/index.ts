/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

import * as functions from "firebase-functions/v2";
import { initializeApp } from "firebase-admin/app";
import cors from "cors";
import axios, { type AxiosResponse } from "axios";
import "dotenv/config";

// Type definitions for OpenRouter API
interface ChatCompletionRequest {
  model: string;
  messages: Array<{
    role: "system" | "user" | "assistant";
    content: string;
  }>;
  max_tokens?: number;
  temperature?: number;
}

interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: "assistant" | "user" | "system";
      content: string;
    };
    finish_reason: string | null;
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Initialize Firebase Admin
initializeApp();

// Initialize CORS middleware
const allowedOrigins = [
  "http://localhost:3000",
  "https://social-goods-6e6e9.web.app",
  "https://social-goods-6e6e9.firebaseapp.com",
  "https://social-goods-6e6e9--staging.web.app",
  "https://social-goods-6e6e9--staging.firebaseapp.com",
  /^https:\/\/social-goods-.*--social-goods-web.*\.web\.app$/,
  /^https:\/\/social-goods-.*--social-goods-web.*\.firebaseapp\.com$/,
];

const corsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    if (
      allowedOrigins.some((allowedOrigin) => {
        if (typeof allowedOrigin === "string") {
          return origin === allowedOrigin;
        }
        return allowedOrigin.test(origin);
      })
    ) {
      return callback(null, true);
    }

    console.error(`CORS blocked for origin: ${origin}`);
    return callback(new Error("Not allowed by CORS"));
  },
  methods: ["GET", "POST", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"],
  credentials: true,
};

const corsHandler = cors(corsOptions);

// OpenRouter configuration
const OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions";
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || "";

if (!OPENROUTER_API_KEY) {
  console.error("OpenRouter API key is not set");
  throw new Error("OpenRouter API key is not configured");
}

// Model configuration
const MODEL_NAME = "openai/gpt-4.1-mini";
const MAX_TOKENS = 2048;
const TEMPERATURE = 0.7;

/**
 * Cloud Function to handle ethical score requests
 */
export const getEthicalScore = functions.https.onRequest((req, res) => {
  // Use CORS middleware
  return corsHandler(req, res, async () => {
    try {
      // Validate request method
      if (req.method !== "POST") {
        res.status(405).json({ error: "Method not allowed" });
        return;
      }

      // Validate request body
      const { brand } = req.body;
      if (!brand || typeof brand !== "string") {
        res.status(400).json({ error: "Missing or invalid brand name" });
        return;
      }

      console.log("Received ethical score request for brand:", brand);

      const prompt = `Please evaluate ${brand} across Labor Practices, Sustainability, Ethical Sourcing, Charitable Donations, and Overall Sentiment. 
    For each dimension, provide a score from 1-5 (5 being best) and a brief summary. 
    Also indicate if the company makes charitable donations.
    
    IMPORTANT: Your response must be ONLY valid JSON with no additional text before or after. 
    
    Respond in this exact JSON format: {
      "laborScore": 3,
      "sustainabilityScore": 4,
      "sourcingScore": 3,
      "hasDonations": true,
      "summary": "Brief overall summary of the brand's ethical practices.",
      "sources": ["https://example.com/source1"]
    }`;

      // Prepare the request for OpenRouter
      const requestData = {
        model: MODEL_NAME,
        messages: [
          {
            role: "system",
            content:
              "You are an expert in ethical business practices and sustainability. Provide accurate, unbiased assessments of brands. Respond with only the JSON object, no additional text.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: MAX_TOKENS,
        temperature: TEMPERATURE,
      };

      console.log("Sending request to OpenRouter...");

      const response: AxiosResponse<ChatCompletionResponse> = await axios.post(
        OPENROUTER_API_URL,
        requestData as ChatCompletionRequest,
        {
          headers: {
            Authorization: `Bearer ${OPENROUTER_API_KEY}`,
            "Content-Type": "application/json",
            "HTTP-Referer": "https://your-site-url.com", // Replace with your site URL
            "X-Title": "Social Goods App", // Your app name
          },
        }
      );

      console.log("Received response from OpenRouter");

      // Extract the response
      const responseData = response.data;
      const responseText = responseData.choices[0]?.message?.content;

      if (!responseText) {
        throw new Error("No response text received from the model");
      }

      // Try to extract JSON from the response
      let jsonContent;
      try {
        // First try to parse the entire text as JSON
        JSON.parse(responseText);
        jsonContent = responseText;
      } catch (e) {
        // If that fails, try to find JSON-like content between curly braces
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          try {
            // Validate that it's actually JSON
            JSON.parse(jsonMatch[0]);
            jsonContent = jsonMatch[0];
          } catch (e) {
            console.error("Found JSON-like content but it was invalid:", e);
            throw new Error("Invalid JSON in response");
          }
        } else {
          throw new Error("No valid JSON found in response");
        }
      }

      const result = JSON.parse(jsonContent);

      // Calculate overall score
      const overallScore =
        (result.laborScore + result.sustainabilityScore + result.sourcingScore) / 3;

      // Helper function to convert 1-5 rating to A-D scale
      const convertToRating = (score: number) => {
        if (score >= 4.5) return "A";
        if (score >= 3.5) return "B";
        if (score >= 2.5) return "C";
        return "D";
      };

      // Helper function to convert 1-5 rating to color
      const convertToColor = (score: number) => {
        if (score >= 4) return "green";
        if (score >= 2.5) return "yellow";
        return "red";
      };

      console.log("Sending response to client");
      res.status(200).json({
        labor_practices: {
          summary: `Labor practices score: ${result.laborScore}/5`,
          rating: convertToColor(result.laborScore),
        },
        sustainability: {
          summary: `Sustainability score: ${result.sustainabilityScore}/5`,
          rating: convertToColor(result.sustainabilityScore),
        },
        ethical_sourcing: {
          summary: `Ethical sourcing score: ${result.sourcingScore}/5`,
          rating: convertToColor(result.sourcingScore),
        },
        donations: {
          summary: result.hasDonations
            ? "Engages in charitable giving"
            : "No significant charitable giving",
          rating: result.hasDonations ? "green" : "yellow",
        },
        overall_sentiment: {
          summary: result.summary || "No overall sentiment provided.",
        },
        overall_rating: convertToRating(overallScore),
        sources: Array.isArray(result.sources) ? result.sources : [],
      });
    } catch (err: unknown) {
      console.error("Error in getEthicalScore:", err);
      const error = err as Error;
      res.status(500).json({
        error: "Internal Server Error",
        details: error.message,
      });
    }
  });
});

/**
 * Cloud Function to handle alternative products requests
 */
export const getAlternativeProducts = functions.https.onRequest((req, res) => {
  // Use CORS middleware
  return corsHandler(req, res, async () => {
    try {
      // Validate request method
      if (req.method !== "POST") {
        res.status(405).json({ error: "Method not allowed" });
        return;
      }

      // Validate request body
      const { category, currentBrand, currentRating } = req.body;
      if (
        !category ||
        typeof category !== "string" ||
        !currentBrand ||
        typeof currentBrand !== "string" ||
        !["A", "B", "C", "D"].includes(currentRating)
      ) {
        res.status(400).json({ error: "Missing or invalid request parameters" });
        return;
      }

      console.log("Received alternative products request for:", {
        category,
        currentBrand,
        currentRating,
      });

      const prompt = `Provide 3-5 ethical alternative products to "${currentBrand}" in the "${category}" category. 
The current brand has an ethical rating of ${currentRating}.

Return a JSON array of objects with the following structure:
[
  {
    "name": "Product Name",
    "brand": "Brand Name",
    "description": "Brief description of the product and why it's ethical.",
    "price": "$" | "$$" | "$$$" | "$$$$",
    "search_url": "https://www.google.com/search?q=brand+name+product+name&tbm=shop",
    "image_url": "https://example.com/image.jpg",
    "overall_rating": "A" | "B" | "C" | "D",
    "reason": "Why this is better than the current brand"
  }
]`;

      // Prepare the request for OpenRouter
      const requestData = {
        model: MODEL_NAME,
        messages: [
          {
            role: "system",
            content:
              "You are an expert in ethical consumer products. Provide accurate, unbiased recommendations for ethical alternatives to products. Respond with only the JSON array, no additional text.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: MAX_TOKENS,
        temperature: TEMPERATURE,
      };

      console.log("Sending request to OpenRouter...");

      const response = await axios.post<ChatCompletionResponse>(OPENROUTER_API_URL, requestData, {
        headers: {
          Authorization: `Bearer ${OPENROUTER_API_KEY}`,
          "Content-Type": "application/json",
          "HTTP-Referer": "https://your-site-url.com",
          "X-Title": "Social Goods App",
        },
      });

      console.log("Received response from OpenRouter");

      // Extract and validate the response
      const responseText = response.data.choices[0]?.message?.content;
      if (!responseText) {
        throw new Error("No response text received from the model");
      }

      // Try to extract JSON from the response
      let jsonContent;
      try {
        // First try to parse the entire text as JSON
        JSON.parse(responseText);
        jsonContent = responseText;
      } catch (e) {
        // If that fails, try to find JSON-like content between square brackets
        const jsonMatch = responseText.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          try {
            // Validate that it's actually JSON
            JSON.parse(jsonMatch[0]);
            jsonContent = jsonMatch[0];
          } catch (e) {
            console.error("Found JSON-like content but it was invalid:", e);
            throw new Error("Invalid JSON in response");
          }
        } else {
          throw new Error("No valid JSON array found in response");
        }
      }

      const alternatives = JSON.parse(jsonContent);
      if (!Array.isArray(alternatives)) {
        throw new Error("Expected an array of alternatives");
      }

      console.log("Sending response to client");
      res.status(200).json(alternatives);
    } catch (err: unknown) {
      console.error("Error in getAlternativeProducts:", err);
      const error = err as Error;
      res.status(500).json({
        error: "Internal Server Error",
        details: error.message,
      });
    }
  });
});

// Health check endpoint
export const healthCheck = functions.https.onRequest((req, res) => {
  res.status(200).json({ status: "ok" });
});
