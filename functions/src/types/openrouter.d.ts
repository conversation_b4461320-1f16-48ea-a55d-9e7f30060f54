declare namespace OpenRouter {
  interface ChatCompletionRequest {
    model: string;
    messages: Array<{
      role: "system" | "user" | "assistant";
      content: string;
    }>;
    max_tokens?: number;
    temperature?: number;
    top_p?: number;
    n?: number;
    stop?: string | string[];
    presence_penalty?: number;
    frequency_penalty?: number;
    logit_bias?: { [key: string]: number };
    user?: string;
  }

  interface ChatCompletionResponse {
    id: string;
    object: "chat.completion";
    created: number;
    model: string;
    choices: Array<{
      index: number;
      message: {
        role: "assistant" | "user" | "system";
        content: string;
      };
      finish_reason: "stop" | "length" | "content_filter" | "tool_calls" | "function_call" | null;
    }>;
    usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    };
  }

  interface ErrorResponse {
    error: {
      message: string;
      type: string;
      param: string | null;
      code: string | null;
    };
  }
}

export = OpenRouter;
export as namespace OpenRouter;
