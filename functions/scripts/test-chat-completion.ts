import axios from "axios";
import * as dotenv from "dotenv";

// Load environment variables
dotenv.config({ path: "../.env" });

// Configuration
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;
const LOCAL_FUNCTION_URL = "http://localhost:5001/YOUR-PROJECT-ID/us-central1/chatCompletion";
const DEPLOYED_FUNCTION_URL = "YOUR-DEPLOYED-FUNCTION-URL";

// Choose which URL to test (local or deployed)
const TARGET_URL = process.env.TEST_ENV === "local" ? LOCAL_FUNCTION_URL : DEPLOYED_FUNCTION_URL;

async function testChatCompletion() {
  if (!OPENROUTER_API_KEY) {
    console.error("Error: OPENROUTER_API_KEY is not set in the environment variables");
    process.exit(1);
  }

  console.log(`Testing chat completion function at: ${TARGET_URL}`);

  const testMessage = {
    messages: [
      { role: "system", content: "You are a helpful assistant." },
      { role: "user", content: "Hello! Can you tell me a short joke?" },
    ],
    max_tokens: 100,
    temperature: 0.7,
  };

  try {
    const response = await axios.post(TARGET_URL, testMessage, {
      headers: {
        "Content-Type": "application/json",
        "x-user-id": "test-user-123", // Example user ID for testing
      },
    });

    console.log("✅ Test successful!");
    console.log("Response status:", response.status);
    console.log("Response data:", JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error("❌ Test failed:");

    if (axios.isAxiosError(error)) {
      console.error("Error status:", error.response?.status);
      console.error("Error data:", error.response?.data);
      console.error("Error message:", error.message);
    } else if (error instanceof Error) {
      console.error("Error:", error.message);
    } else {
      console.error("Unknown error:", error);
    }

    process.exit(1);
  }
}

// Run the test
testChatCompletion();
