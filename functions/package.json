{"name": "functions", "version": "1.0.0", "description": "Cloud Functions for Firebase", "type": "module", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "npm run build && firebase deploy --only functions", "logs": "firebase functions:log", "test:local": "ts-node scripts/test-chat-completion.ts", "test:deployed": "TEST_ENV=deployed ts-node scripts/test-chat-completion.ts"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"@google-cloud/storage": "^7.16.0", "axios": "^1.6.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "firebase-admin": "^13.4.0", "firebase-functions": "^6.3.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/node": "22.15.31", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^7.3.1", "eslint": "^8.57.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "firebase-functions-test": "^3.1.0", "prettier": "^3.2.5", "typescript": "^5.3.3"}, "private": true}