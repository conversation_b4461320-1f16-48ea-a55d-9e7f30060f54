{"compilerOptions": {"module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "noImplicitReturns": true, "noUnusedLocals": true, "outDir": "lib", "sourceMap": true, "strict": true, "target": "ES2020", "skipLibCheck": true, "types": ["node"], "typeRoots": ["./node_modules/@types", "./src/types"], "resolveJsonModule": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true}, "compileOnSave": true, "include": ["src"], "exclude": ["node_modules"]}