-- Create the brand_scores table
CREATE TABLE IF NOT EXISTS public.brand_scores (
  id SERIAL PRIMARY KEY,
  brand TEXT NOT NULL,
  score NUMERIC,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  score_data JSONB,
  labor_practices_summary TEXT,
  labor_practices_rating TEXT,
  sustainability_summary TEXT,
  sustainability_rating TEXT,
  ethical_sourcing_summary TEXT,
  ethical_sourcing_rating TEXT,
  donations_summary TEXT,
  donations_rating TEXT,
  overall_sentiment_summary TEXT,
  overall_rating TEXT,
  sources JSONB
);

-- Create an index on the brand column for faster lookups
CREATE INDEX IF NOT EXISTS idx_brand_scores_brand ON public.brand_scores (brand);

-- Create the alternative_products table for alternative product recommendations
CREATE TABLE IF NOT EXISTS public.alternative_products (
  id SERIAL PRIMARY KEY,
  cache_key TEXT,
  category TEXT NOT NULL,
  current_brand TEXT NOT NULL,
  current_rating TEXT NOT NULL,
  alternatives_data JSONB,
  last_updated TIMES<PERSON>MP WITH TIME ZONE DEFAULT NOW()
);

-- Create an index for faster lookups
CREATE INDEX IF NOT EXISTS idx_alternative_products_lookup ON public.alternative_products (category, current_brand, current_rating);
CREATE INDEX IF NOT EXISTS idx_alternative_products_cache_key ON public.alternative_products (cache_key);
