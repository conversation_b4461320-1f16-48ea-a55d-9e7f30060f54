// / <reference types="chrome"/>

// Function to initialize API keys from environment variables
async function initializeApiKeys() {
  try {
    // Get API keys from environment variables (injected at build time via webpack)
    const openaiApiKey = process.env.OPENAI_API_KEY || "";
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL || "";
    const supabaseAnonKey =
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY || "";

    // Store them in chrome.storage.local
    await chrome.storage.local.set({
      OPENAI_API_KEY: openaiApiKey,
      SUPABASE_URL: supabaseUrl,
      SUPABASE_ANON_KEY: supabaseAnonKey,
    });

    console.log("GoodGoods: API keys initialized from environment variables");

    // Log key previews for debugging (first few characters only for security)
    if (openaiApi<PERSON>ey) {
      console.log(
        "GoodGoods: OpenAI API key loaded:",
        `Key found: ${openaiApiKey.substring(0, 5)}...`
      );
    } else {
      console.log("GoodGoods: No OpenAI API key found in environment variables");
    }

    if (supabaseUrl && supabaseAnonKey) {
      console.log("GoodGoods: Supabase credentials loaded");
    } else {
      console.log("GoodGoods: Missing Supabase credentials in environment variables");
    }
  } catch (error) {
    console.error(
      "GoodGoods: Error initializing API keys:",
      error instanceof Error ? error.message : JSON.stringify(error)
    );
  }
}

// Listen for installation
chrome.runtime.onInstalled.addListener(async () => {
  console.log("GoodGoods extension installed");

  // Initialize API keys from environment variables
  await initializeApiKeys();
});

// Listen for messages from content script
chrome.runtime.onMessage.addListener(
  (request: any, _sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) => {
    console.log("GoodGoods: Background script received message", request);

    // Handle opening a new tab
    if (request.type === "openTab" && request.url) {
      console.log("GoodGoods: Opening tab with URL:", request.url);
      chrome.tabs.create({ url: request.url }, (tab) => {
        sendResponse({ success: true, tabId: tab?.id });
      });
      return true; // Keep the message channel open for async response
    }

    // Handle API key initialization request
    if (request.type === "initApiKeys") {
      console.log("GoodGoods: Received request to initialize API keys");
      initializeApiKeys()
        .then(() => {
          sendResponse({ success: true });
        })
        .catch((error) => {
          console.error("GoodGoods: Error initializing API keys:", error);
          sendResponse({ success: false, error: String(error) });
        });
      return true; // Keep the message channel open for async response
    }

    // Handle API key retrieval request
    if (request.type === "getApiKeys") {
      console.log("GoodGoods: Received request to get API keys");
      chrome.storage.local
        .get(["OPENAI_API_KEY", "SUPABASE_URL", "SUPABASE_ANON_KEY"])
        .then((result) => {
          sendResponse({
            success: true,
            keys: {
              openaiApiKey: result.OPENAI_API_KEY || "",
              supabaseUrl: result.SUPABASE_URL || "",
              supabaseAnonKey: result.SUPABASE_ANON_KEY || "",
            },
          });
        })
        .catch((error) => {
          console.error("GoodGoods: Error getting API keys:", error);
          sendResponse({ success: false, error: String(error) });
        });
      return true; // Keep the message channel open for async response
    }

    return true;
  }
);
