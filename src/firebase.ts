import { initializeApp } from "firebase/app";
import {
  getA<PERSON>,
  GoogleAuthProvider,
  FacebookAuthProvider,
  OAuthProvider,
  connectAuthEmulator,
  type User,
  type UserCredential,
} from "firebase/auth";
import { getFunctions, connectFunctionsEmulator } from "firebase/functions";

export type { User } from "firebase/auth";

const firebaseConfig = {
  apiKey: "AIzaSyCPMrKR1y3tf-opDg8J0Edhriqs2q8FcCA",
  authDomain: "swai-3136c.firebaseapp.com",
  projectId: "swai-3136c",
  storageBucket: "swai-3136c.firebasestorage.app",
  messagingSenderId: "753848377135",
  appId: "1:753848377135:web:bd03569bfd722260780bda",
  measurementId: "G-Z37EK74VP2",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const functions = getFunctions(app);

// Initialize Auth and Providers
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();
export const facebookProvider = new FacebookAuthProvider();
// Apple provider requires additional configuration in Firebase Console
export const appleProvider = new OAuthProvider("apple.com");

// If in development, connect to local emulators
if (process.env.NODE_ENV === "development") {
  connectFunctionsEmulator(functions, "localhost", 5001);
  connectAuthEmulator(auth, "http://localhost:9099");
}

export { app, functions };
