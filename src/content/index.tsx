import React, { useState } from "react";
import { createRoot } from "react-dom/client";
import { ScoreBadge } from "../components/ScoreBadge";
import { detectBrand } from "../utils/brandDetection";
import { getScoreFromCache, saveScoreToCache } from "../utils/supabase";
import { getEthicalScore } from "../utils/openai";
import type { CachedScore } from "../types";
import { AuthProvider } from "../contexts/AuthContext";
import AuthPanel from "../components/Auth/AuthModal";

// Function to detect product category from the page
function detectProductCategory(): string {
  try {
    // Try to get category from breadcrumbs
    const breadcrumbs = document.querySelectorAll(
      "#wayfinding-breadcrumbs_feature_div .a-link-normal"
    );
    if (breadcrumbs && breadcrumbs.length > 0) {
      // Get the second-to-last breadcrumb (often the category)
      const categoryIndex = Math.max(0, breadcrumbs.length - 2);
      const categoryText = breadcrumbs[categoryIndex].textContent?.trim();
      if (categoryText && categoryText.length > 0) {
        return categoryText;
      }
    }

    // Try to get from product classification
    const classificationElements = document.querySelectorAll(
      "#productDetails_detailBullets_sections1 th"
    );
    for (let i = 0; i < classificationElements.length; i++) {
      const element = classificationElements[i];
      if (element.textContent?.includes("Best Sellers Rank")) {
        const rankText = element.nextElementSibling?.textContent;
        if (rankText) {
          const categoryMatch = rankText.match(/in\s+([^(]+)/);
          if (categoryMatch && categoryMatch[1]) {
            return categoryMatch[1].trim();
          }
        }
      }
    }

    // Fallback to generic category
    return "product";
  } catch (error) {
    console.error("GoodGoods: Error detecting product category:", error);
    return "product";
  }
}

// Add basic CSS styles
const injectStyles = () => {
  const styleElement = document.createElement("style");
  styleElement.textContent = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    #goodgoods-score {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      --emerald-500: #10B981;
      --emerald-600: #059669;
      --emerald-700: #047857;
      --emerald-800: #065F46;
      --emerald-50: #ECFDF5;
      --emerald-100: #D1FAE5;
      --blue-500: #3B82F6;
      --blue-600: #2563EB;
      --blue-700: #1D4ED8;
      --amber-500: #F59E0B;
      --amber-600: #D97706;
      --amber-50: #FFFBEB;
      --red-500: #EF4444;
      --red-600: #DC2626;
      --red-50: #FEF2F2;
      --gray-50: #F9FAFB;
      --gray-100: #F3F4F6;
      --gray-200: #E5E7EB;
      --gray-300: #D1D5DB;
      --gray-400: #9CA3AF;
      --gray-500: #6B7280;
      --gray-600: #4B5563;
      --gray-700: #374151;
      --gray-800: #1F2937;
      --gray-900: #111827;
    }

    #goodgoods-score .fixed {
      position: fixed;
      top: 1rem;
      right: 1rem;
      z-index: 9999;
    }

    #goodgoods-score .rounded-xl {
      border-radius: 0.75rem;
    }

    #goodgoods-score .shadow-lg {
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    #goodgoods-score .overflow-hidden {
      overflow: hidden;
    }

    #goodgoods-score .cursor-pointer {
      cursor: pointer;
    }

    /* Loading spinner animation */
    #goodgoods-score .spinner {
      display: inline-block;
      width: 24px;
      height: 24px;
      border: 3px solid var(--gray-200);
      border-top-color: var(--emerald-500);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    /* Transition effects */
    #goodgoods-score .transition {
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 200ms;
    }

    /* Hover effects */
    #goodgoods-score .hover-shadow:hover {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    #goodgoods-score .hover-underline:hover {
      text-decoration: underline;
    }

    /* Loading state */
    #goodgoods-score .loading-badge {
      background-color: white;
      border-radius: 0.75rem;
      padding: 1rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    #goodgoods-score .loading-text {
      font-size: 1rem;
      font-weight: 600;
      color: var(--gray-700);
    }

    /* Error state */
    #goodgoods-score .error-badge {
      background-color: var(--red-50);
      border-radius: 0.75rem;
      padding: 1rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      border-left: 4px solid var(--red-500);
    }

    #goodgoods-score .error-title {
      font-size: 1rem;
      font-weight: 600;
      color: var(--red-600);
      margin-bottom: 0.25rem;
    }

    #goodgoods-score .error-message {
      font-size: 0.875rem;
      color: var(--gray-600);
    }
  `;
  document.head.appendChild(styleElement);
};

async function injectScoreBadge() {
  console.log("GoodGoods: Content script running");

  // Check if we're on a product page
  if (!window.location.pathname.includes("/dp/")) {
    console.log("GoodGoods: Not a product page, skipping");
    return;
  }

  // Check if badge is already injected
  if (document.getElementById("goodgoods-score")) {
    console.log("GoodGoods: Badge already injected, skipping");
    return;
  }

  // Inject CSS styles
  injectStyles();

  // Detect the brand
  const brandResult = detectBrand();
  if (!brandResult) {
    console.log("GoodGoods: Could not detect brand");
    return;
  }

  try {
    // Create container for our component
    const container = document.createElement("div");
    container.id = "goodgoods-score";
    document.body.appendChild(container);

    // Show loading state
    const root = createRoot(container);
    root.render(
      <React.StrictMode>
        <AuthProvider>
          <ContentUI brandResult={brandResult} />
        </AuthProvider>
      </React.StrictMode>
    );
  } catch (error) {
    console.error("GoodGoods: Error:", error);
  }
}

// New ContentUI component to manage auth state and render ScoreBadge or AuthPanel
const ContentUI: React.FC<{ brandResult: { brand: string; confidence: number } }> = ({
  brandResult,
}) => {
  const [showAuth, setShowAuth] = useState(false);
  const [score, setScore] = useState<CachedScore | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  React.useEffect(() => {
    let isMounted = true;
    async function fetchScore() {
      setLoading(true);
      setError(null);
      let cached = await getScoreFromCache(brandResult.brand);
      if (!cached) {
        try {
          const ethicalScore = await getEthicalScore(brandResult.brand);
          cached = {
            ...ethicalScore,
            brand: brandResult.brand,
            last_updated: new Date().toISOString(),
          };
          saveScoreToCache(cached as CachedScore).catch(() => {});
        } catch (apiError) {
          if (isMounted) setError("Error connecting to OpenAI API. Please try again later.");
        }
      }
      if (isMounted) setScore(cached as CachedScore);
      if (isMounted) setLoading(false);
    }
    fetchScore();
    return () => {
      isMounted = false;
    };
  }, [brandResult.brand]);

  if (loading) {
    return (
      <div className="fixed top-4 right-4 z-50">
        <div className="loading-badge">
          <div className="spinner"></div>
          <div className="loading-text">Loading ethical data for {brandResult.brand}...</div>
        </div>
      </div>
    );
  }
  if (error) {
    return (
      <div className="fixed top-4 right-4 z-50">
        <div className="error-badge">
          <div className="error-title">Error loading data</div>
          <div className="error-message">{error}</div>
        </div>
      </div>
    );
  }
  if (showAuth) {
    return (
      <div className="fixed top-4 right-4 z-50">
        <AuthPanel onClose={() => setShowAuth(false)} />
      </div>
    );
  }
  return (
    <ScoreBadge
      score={score as CachedScore}
      productCategory={detectProductCategory()}
      brand={brandResult.brand}
      onSignInClick={() => setShowAuth(true)}
    />
  );
};

// Function to ensure API keys are initialized
async function ensureApiKeysInitialized(): Promise<void> {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ type: "initApiKeys" }, (response) => {
      if (response && response.success) {
        console.log("GoodGoods: API keys initialized successfully");
      } else {
        console.warn(
          "GoodGoods: API key initialization may have failed:",
          response ? response.error : "Unknown error"
        );
      }
      resolve();
    });
  });
}

// Run when the page loads
console.log("GoodGoods: Content script loaded");

// Initialize API keys and then inject the score badge
async function initialize() {
  try {
    // First ensure API keys are initialized
    await ensureApiKeysInitialized();

    // Then inject the score badge
    injectScoreBadge();
  } catch (error) {
    console.error("GoodGoods: Error during initialization:", error);
    // Still try to inject the score badge even if API key initialization fails
    injectScoreBadge();
  }
}

// Wait for the page to be fully loaded
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", initialize);
} else {
  initialize();
}
