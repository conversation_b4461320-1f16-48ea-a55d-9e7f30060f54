import React, { useState } from "react";
import { GoogleIcon, FacebookIcon, AppleIcon } from "../Icons";
import { useAuth } from "../../contexts/AuthContext";
import {
  signInWithGoogle,
  signInWithFacebook,
  signInWithApple,
  signInWithEmail,
  registerWithEmail,
  resetPassword,
} from "../../services/auth";

interface AuthFormProps {
  initialMode?: "signin" | "signup" | "reset";
  className?: string;
}

const AuthForm: React.FC<AuthFormProps> = ({ initialMode = "signin", className = "" }) => {
  type AuthMode = "signin" | "signup" | "reset";
  const [mode, setMode] = useState<AuthMode>(
    (initialMode === "signin" || initialMode === "signup" || initialMode === "reset"
      ? initialMode
      : "signin") as AuthMode
  );
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const { currentUser } = useAuth();

  const resetForm = () => {
    setEmail("");
    setPassword("");
    setName("");
    setError("");
    setMessage("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setMessage("");

    try {
      if (mode === "reset") {
        await resetPassword(email);
        setMessage("Password reset email sent. Please check your inbox.");
        setMode("signin");
      } else if (mode === "signin") {
        await signInWithEmail(email, password);
      } else {
        await registerWithEmail(email, password, name);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : "An unknown error occurred");
    } finally {
      setLoading(false);
    }
  };

  const handleSocialSignIn = async (provider: () => Promise<unknown>) => {
    setError("");
    setLoading(true);
    try {
      await provider();
    } catch (error) {
      setError(error instanceof Error ? error.message : "Authentication failed");
    } finally {
      setLoading(false);
    }
  };

  const socialProviders = [
    {
      name: "Google",
      icon: <GoogleIcon className="h-5 w-5" />,
      provider: signInWithGoogle,
    },
    {
      name: "Facebook",
      icon: <FacebookIcon className="h-5 w-5" />,
      provider: signInWithFacebook,
    },
    {
      name: "Apple",
      icon: <AppleIcon className="h-5 w-5" />,
      provider: signInWithApple,
    },
  ] as const;

  return (
    <div className={`auth-form ${className}`}>
      <h2 className="text-xl font-bold text-gray-900 mb-1 text-center">
        {mode === "signin"
          ? "Sign in to continue"
          : mode === "signup"
          ? "Create your account"
          : "Reset Password"}
      </h2>
      <p className="text-sm text-gray-600 mb-6 text-center">
        {mode === "signin"
          ? "Sign in to view your profile and preferences"
          : mode === "signup"
          ? "Create an account to get started"
          : "Enter your email to reset your password"}
      </p>

      {error && <div className="bg-red-50 text-red-700 text-sm p-3 rounded-md mb-4">{error}</div>}
      {message && <div className="bg-green-50 text-green-700 text-sm p-3 rounded-md mb-4">{message}</div>}

      <div className="space-y-4">
        <div className="grid gap-3">
          {socialProviders.map(({ name, icon, provider }) => (
            <button
              key={name}
              onClick={() => handleSocialSignIn(provider)}
              disabled={loading}
              className="w-full flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {icon}
              <span>Continue with {name}</span>
            </button>
          ))}
        </div>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or continue with</span>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {mode === "signup" && (
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Full Name
              </label>
              <input
                id="name"
                type="text"
                required
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                placeholder="John Doe"
                disabled={loading}
              />
            </div>
          )}

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email address
            </label>
            <input
              id="email"
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="<EMAIL>"
              disabled={loading}
            />
          </div>

          {mode !== "reset" && (
            <div>
              <div className="flex items-center justify-between">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                {mode === "signin" && (
                  <button
                    type="button"
                    onClick={() => {
                      resetForm();
                      setMode("reset");
                    }}
                    className="text-xs text-blue-600 hover:text-blue-500"
                  >
                    Forgot password?
                  </button>
                )}
              </div>
              <input
                id="password"
                type="password"
                autoComplete={mode === "signup" ? "new-password" : "current-password"}
                required={mode === "signin" || mode === "signup"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                placeholder="••••••••"
                disabled={loading}
              />
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading
                ? "Loading..."
                : mode === "signin"
                ? "Sign in"
                : mode === "signup"
                ? "Sign up"
                : "Send reset link"}
            </button>
          </div>

          <div className="mt-4 text-center text-sm">
            {mode === "signin" ? (
              <p>
                Don&apos;t have an account?{" "}
                <button
                  type="button"
                  onClick={() => {
                    resetForm();
                    setMode("signup");
                  }}
                  className="font-medium text-blue-600 hover:text-blue-500"
                >
                  Sign up
                </button>
              </p>
            ) : mode === "signup" ? (
              <p>
                Already have an account?{" "}
                <button
                  type="button"
                  onClick={() => {
                    resetForm();
                    setMode("signin");
                  }}
                  className="font-medium text-blue-600 hover:text-blue-500"
                >
                  Sign in
                </button>
              </p>
            ) : (
              <p>
                Remember your password?{" "}
                <button
                  type="button"
                  onClick={() => {
                    resetForm();
                    setMode("signin");
                  }}
                  className="font-medium text-blue-600 hover:text-blue-500"
                >
                  Sign in
                </button>
              </p>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

export default AuthForm;
