import React from "react";
import { useAuth } from "../../contexts/AuthContext";

interface SignInButtonProps {
  className?: string;
  variant?: "primary" | "secondary" | "outline";
  size?: "sm" | "md" | "lg";
  children?: React.ReactNode;
  onSignInClick?: () => void;
}

const SignInButton: React.FC<SignInButtonProps> = ({
  className = "",
  variant = "primary",
  size = "md",
  children = "Sign in",
  onSignInClick,
}) => {
  const { currentUser, isAnonymous } = useAuth();

  if (currentUser && !isAnonymous) {
    // User is signed in and not anonymous
    return null;
  }

  // Inline style objects for size
  const sizeStyles: Record<string, React.CSSProperties> = {
    sm: { padding: "6px 12px", fontSize: 14 },
    md: { padding: "8px 16px", fontSize: 16 },
    lg: { padding: "12px 24px", fontSize: 18 },
  };

  // Inline style objects for variant
  const variantStyles: Record<string, React.CSSProperties> = {
    primary: {
      background: "#4f46e5",
      color: "#fff",
      border: "none",
      fontWeight: 600,
      transition: "background 0.2s, box-shadow 0.2s",
      boxShadow: "0 1px 4px rgba(0,0,0,0.07)",
    },
    secondary: {
      background: "#fff",
      color: "#4f46e5",
      border: "1px solid #4f46e5",
      fontWeight: 600,
      transition: "background 0.2s, box-shadow 0.2s",
      boxShadow: "0 1px 4px rgba(0,0,0,0.07)",
    },
    outline: {
      background: "#fff",
      color: "#374151",
      border: "1px solid #d1d5db",
      fontWeight: 500,
      transition: "background 0.2s, box-shadow 0.2s",
      boxShadow: "0 1px 4px rgba(0,0,0,0.07)",
    },
  };

  // Base button style
  const baseStyle: React.CSSProperties = {
    display: "inline-flex",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 6,
    fontFamily: "inherit",
    cursor: "pointer",
    outline: "none",
    margin: 0,
    userSelect: "none",
    minWidth: 0,
    minHeight: 0,
  };

  // Merge styles
  const buttonStyle: React.CSSProperties = {
    ...baseStyle,
    ...sizeStyles[size],
    ...variantStyles[variant],
  };

  return (
    <button
      type="button"
      onClick={onSignInClick}
      style={buttonStyle}
      onMouseOver={(e) => {
        if (variant === "primary") e.currentTarget.style.background = "#3730a3";
        if (variant === "secondary") e.currentTarget.style.background = "#f3f4f6";
        if (variant === "outline") e.currentTarget.style.background = "#f3f4f6";
      }}
      onMouseOut={(e) => {
        if (variant === "primary") e.currentTarget.style.background = "#4f46e5";
        if (variant === "secondary") e.currentTarget.style.background = "#fff";
        if (variant === "outline") e.currentTarget.style.background = "#fff";
      }}
    >
      {children}
    </button>
  );
};

export default SignInButton;
