import React, { useState } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { GoogleIcon, FacebookIcon, AppleIcon } from "../Icons";
import { useAuth } from "../../contexts/AuthContext";
import {
  signInWithGoogle,
  signInWithFacebook,
  signInWithApple,
  signInWithEmail,
  registerWithEmail,
  resetPassword,
} from "../../services/auth";

interface AuthPanelProps {
  onClose: () => void;
  initialMode?: "signin" | "signup" | "reset";
  className?: string;
}

const AuthPanel: React.FC<AuthPanelProps> = ({
  onClose,
  initialMode = "signin",
  className = "",
}) => {
  type AuthMode = "signin" | "signup" | "reset";
  const [mode, setMode] = useState<AuthMode>(
    (initialMode === "signin" || initialMode === "signup" || initialMode === "reset"
      ? initialMode
      : "signin") as AuthMode
  );
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const { currentUser } = useAuth();

  const resetForm = () => {
    setEmail("");
    setPassword("");
    setName("");
    setError("");
    setMessage("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setMessage("");

    try {
      if (mode === "reset") {
        await resetPassword(email);
        setMessage("Password reset email sent. Please check your inbox.");
        setMode("signin");
      } else if (mode === "signin") {
        await signInWithEmail(email, password);
      } else {
        await registerWithEmail(email, password, name);
      }
      onClose();
    } catch (error) {
      setError(error instanceof Error ? error.message : "An unknown error occurred");
    } finally {
      setLoading(false);
    }
  };

  const handleSocialSignIn = async (provider: () => Promise<unknown>) => {
    setError("");
    setLoading(true);
    try {
      await provider();
      onClose();
    } catch (error) {
      setError(error instanceof Error ? error.message : "Authentication failed");
    } finally {
      setLoading(false);
    }
  };

  const socialProviders = [
    {
      name: "Google",
      icon: <GoogleIcon className="h-5 w-5" />,
      provider: signInWithGoogle,
    },
    {
      name: "Facebook",
      icon: <FacebookIcon className="h-5 w-5" />,
      provider: signInWithFacebook,
    },
    {
      name: "Apple",
      icon: <AppleIcon className="h-5 w-5" />,
      provider: signInWithApple,
    },
  ] as const;

  // Panel style (no overlay, no fixed position)
  const panelStyle: React.CSSProperties = {
    position: "relative",
    background: "linear-gradient(135deg, #fff 70%, #f3f4f6 100%)",
    borderRadius: 14,
    boxShadow: "0 8px 32px rgba(0,0,0,0.18)",
    border: "1px solid #e5e7eb",
    padding: "32px 24px 24px 24px",
    width: "100%",
    maxWidth: 360,
    minWidth: 0,
    fontFamily: "inherit",
    margin: "0 auto",
    animation: "modal-in 0.25s cubic-bezier(.4,0,.2,1)",
  };
  const closeBtnStyle: React.CSSProperties = {
    position: "absolute",
    top: 12,
    right: 12,
    background: "rgba(255,255,255,0.85)",
    border: "none",
    borderRadius: "50%",
    width: 36,
    height: 36,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    boxShadow: "0 1px 4px rgba(0,0,0,0.07)",
    color: "#6b7280",
    fontSize: 20,
    transition: "background 0.2s, color 0.2s",
  };
  const titleStyle: React.CSSProperties = {
    fontSize: 22,
    fontWeight: 700,
    color: "#1f2937",
    marginBottom: 4,
    marginTop: 0,
    textAlign: "center",
  };
  const descStyle: React.CSSProperties = {
    color: "#6b7280",
    fontSize: 14,
    marginBottom: 16,
    textAlign: "center",
  };
  const errorStyle: React.CSSProperties = {
    background: "#fef2f2",
    color: "#b91c1c",
    border: "1px solid #fecaca",
    borderRadius: 6,
    padding: "8px 12px",
    margin: "12px 0",
    fontSize: 14,
    display: "flex",
    alignItems: "center",
    gap: 8,
  };
  const messageStyle: React.CSSProperties = {
    background: "#ecfdf5",
    color: "#047857",
    border: "1px solid #a7f3d0",
    borderRadius: 6,
    padding: "8px 12px",
    margin: "12px 0",
    fontSize: 14,
    display: "flex",
    alignItems: "center",
    gap: 8,
  };
  const socialBtnStyle: React.CSSProperties = {
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,
    border: "1px solid #e5e7eb",
    background: "#fff",
    padding: "8px 0",
    fontWeight: 600,
    fontSize: 15,
    cursor: "pointer",
    marginBottom: 8,
    transition: "border 0.2s, box-shadow 0.2s",
  };
  const inputStyle: React.CSSProperties = {
    width: "100%",
    borderRadius: 6,
    border: "1px solid #d1d5db",
    padding: "10px 12px",
    fontSize: 15,
    marginTop: 2,
    marginBottom: 10,
    outline: "none",
    boxSizing: "border-box",
    fontFamily: "inherit",
    background: "#fff",
    transition: "border 0.2s",
  };
  const labelStyle: React.CSSProperties = {
    fontSize: 14,
    fontWeight: 500,
    color: "#374151",
    marginBottom: 2,
    display: "block",
  };
  const submitBtnStyle: React.CSSProperties = {
    width: "100%",
    borderRadius: 8,
    border: "none",
    background: "#4f46e5",
    color: "#fff",
    fontWeight: 600,
    fontSize: 16,
    padding: "10px 0",
    marginTop: 8,
    cursor: "pointer",
    boxShadow: "0 1px 4px rgba(0,0,0,0.07)",
    transition: "background 0.2s",
  };
  const linkBtnStyle: React.CSSProperties = {
    background: "none",
    border: "none",
    color: "#6366f1",
    fontWeight: 500,
    fontSize: 14,
    cursor: "pointer",
    textDecoration: "underline",
    margin: "0 4px",
    padding: 0,
  };
  const dividerStyle: React.CSSProperties = {
    width: "100%",
    borderTop: "1px solid #e5e7eb",
    margin: "18px 0 10px 0",
  };

  return (
    <div style={panelStyle} className={className}>
      <button
        type="button"
        style={closeBtnStyle}
        onClick={onClose}
        aria-label="Close"
        onMouseOver={(e) => (e.currentTarget.style.background = "#f3f4f6")}
        onMouseOut={(e) => (e.currentTarget.style.background = "rgba(255,255,255,0.85)")}
      >
        <span style={{ fontSize: 24, fontWeight: 400, lineHeight: 1 }}>×</span>
      </button>
      <h3 style={titleStyle}>
        {mode === "reset"
          ? "Reset Password"
          : mode === "signin"
            ? "Sign in to GoodGoods"
            : "Create a GoodGoods Account"}
      </h3>
      <div style={descStyle}>
        {mode === "signin"
          ? "Welcome back! Sign in to continue."
          : mode === "signup"
            ? "Join us and make ethical shopping easy."
            : "Enter your email to reset your password."}
      </div>
      {error && (
        <div style={errorStyle}>
          <span style={{ fontSize: 18 }}>❗</span>
          <span>{error}</span>
        </div>
      )}
      {message && (
        <div style={messageStyle}>
          <span style={{ fontSize: 18 }}>✔️</span>
          <span>{message}</span>
        </div>
      )}
      {/* Social sign-in */}
      <div style={{ display: "flex", gap: 8, margin: "18px 0 10px 0" }}>
        {socialProviders.map((provider) => (
          <button
            key={provider.name}
            type="button"
            onClick={() => handleSocialSignIn(provider.provider)}
            disabled={loading}
            style={{
              ...socialBtnStyle,
              flex: 1,
              borderColor:
                provider.name === "Google"
                  ? "#ea4335"
                  : provider.name === "Facebook"
                    ? "#1877f2"
                    : "#111",
              color: provider.name === "Apple" ? "#111" : undefined,
            }}
          >
            {provider.icon}
            <span style={{ marginLeft: 6, fontSize: 15 }}>{provider.name}</span>
          </button>
        ))}
      </div>
      <div style={dividerStyle}></div>
      <form onSubmit={handleSubmit}>
        {mode === "signup" && (
          <div>
            <label htmlFor="name" style={labelStyle}>
              Full Name
            </label>
            <input
              id="name"
              name="name"
              type="text"
              autoComplete="name"
              required
              value={name}
              onChange={(e) => setName(e.target.value)}
              style={inputStyle}
            />
          </div>
        )}
        <div>
          <label htmlFor="email" style={labelStyle}>
            Email address
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            style={inputStyle}
          />
        </div>
        {mode !== "reset" && (
          <div>
            <label htmlFor="password" style={labelStyle}>
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              autoComplete={mode === "signin" ? "current-password" : "new-password"}
              required={(mode as string) !== "reset"}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              style={inputStyle}
            />
          </div>
        )}
        <button type="submit" disabled={loading} style={submitBtnStyle}>
          {loading ? (
            <span style={{ opacity: 0.7 }}>Processing...</span>
          ) : mode === "signin" ? (
            "Sign in"
          ) : mode === "signup" ? (
            "Sign up"
          ) : (
            "Send reset link"
          )}
        </button>
      </form>
      <div style={{ display: "flex", justifyContent: "space-between", marginTop: 16 }}>
        {mode !== "reset" && (
          <button type="button" onClick={() => setMode("reset")} style={linkBtnStyle}>
            Forgot password?
          </button>
        )}
        <button
          type="button"
          onClick={() => {
            const newMode = mode === "signin" ? "signup" : "signin";
            resetForm();
            setMode(newMode);
          }}
          style={linkBtnStyle}
        >
          {mode === "reset"
            ? "Back to sign in"
            : mode === "signin"
              ? "Don't have an account? Sign up"
              : "Already have an account? Sign in"}
        </button>
      </div>
      {/* Modal entrance animation keyframes */}
      <style>{`
        @keyframes modal-in {
          0% { opacity: 0; transform: scale(0.96) translateY(16px); }
          100% { opacity: 1; transform: scale(1) translateY(0); }
        }
      `}</style>
    </div>
  );
};

export default AuthPanel;
