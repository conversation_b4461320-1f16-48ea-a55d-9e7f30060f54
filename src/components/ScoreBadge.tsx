import React, { useState, useEffect } from "react";
import type { EthicalScore, Rating, AlternativeProduct, OverallRating } from "../types";
import { getAlternativeProducts } from "../utils/openai";
import { AlternativeProducts } from "./AlternativeProducts";
import SignInButton from "./Auth/SignInButton";
import { useAuth } from "../contexts/AuthContext";

interface ScoreBadgeProps {
  score: EthicalScore;
  productCategory?: string;
  brand?: string;
  onSignInClick?: () => void;
}

// Updated color palette
const ratingColors: Record<Rating, string> = {
  green: "#10B981", // Emerald
  yellow: "#F59E0B", // Amber
  red: "#EF4444", // Red
};

const ratingBackgroundColors: Record<Rating, string> = {
  green: "#ECFDF5", // Lighter emerald
  yellow: "#FFFBEB", // Lighter amber
  red: "#FEF2F2", // Lighter red
};

// Overall rating colors
const overallRatingColors: Record<OverallRating, string> = {
  A: "#10B981", // Emerald
  B: "#60A5FA", // Blue
  C: "#F59E0B", // Amber
  D: "#EF4444", // Red
};

// Icons for each dimension
const dimensionIcons: Record<string, string> = {
  labor_practices: "👷‍♂️",
  sustainability: "🌱",
  ethical_sourcing: "🔄",
  donations: "💰",
  overall_sentiment: "⭐",
};

export const ScoreBadge: React.FC<ScoreBadgeProps> = ({
  score,
  productCategory = "product",
  brand,
  onSignInClick,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState<"details" | "alternatives">("details");
  const [alternatives, setAlternatives] = useState<AlternativeProduct[]>([]);
  const [isLoadingAlternatives, setIsLoadingAlternatives] = useState(false);
  const [alternativesError, setAlternativesError] = useState<string | null>(null);
  const [showAlternativesSection, setShowAlternativesSection] = useState(false);
  const { isAnonymous } = useAuth();

  // Check if we need to show alternatives (score is B or lower)
  const needsAlternatives = ["B", "C", "D"].includes(score.overall_rating);
  const isTopRated = score.overall_rating === "A";

  // Function to load alternatives
  const loadAlternatives = async (e?: React.MouseEvent) => {
    // If called from an event, prevent propagation
    if (e) e?.stopPropagation();

    if (!needsAlternatives || !brand || showAlternativesSection) return;

    try {
      setIsLoadingAlternatives(true);
      setAlternativesError(null);

      const alternativeProducts = await getAlternativeProducts(
        productCategory,
        brand,
        score.overall_rating
      );

      setAlternatives(alternativeProducts);
      setShowAlternativesSection(true);
    } catch (error) {
      console.error("GoodGoods: Error loading alternatives:", error);
      setAlternativesError("Failed to load alternative products. Please try again.");
    } finally {
      setIsLoadingAlternatives(false);
    }
  };

  // Load alternatives automatically when component expands
  useEffect(() => {
    if (isExpanded && needsAlternatives && !showAlternativesSection) {
      loadAlternatives();
    }
  }, [isExpanded, needsAlternatives, showAlternativesSection, brand, productCategory]);

  // Switch to alternatives tab when they're loaded
  useEffect(() => {
    if (needsAlternatives && showAlternativesSection && alternatives.length > 0) {
      setActiveTab("alternatives");
    }
  }, [showAlternativesSection, alternatives, needsAlternatives]);

  // Helper function to get dimension card style
  const getDimensionCardStyle = (rating: Rating) => ({
    backgroundColor: ratingBackgroundColors[rating],
    borderLeft: `4px solid ${ratingColors[rating]}`,
    borderRadius: "8px",
    padding: "12px",
    marginBottom: "12px",
    boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
  });

  // Get letter grade badge style
  const getLetterGradeStyle = (rating: OverallRating) => ({
    backgroundColor: overallRatingColors[rating],
    color: "white",
    borderRadius: "50%",
    width: "36px",
    height: "36px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    fontWeight: "bold",
    fontSize: "18px",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
  });

  return (
    <div className="fixed top-4 right-4 z-50">
      <div
        className="rounded-xl shadow-lg overflow-hidden cursor-pointer"
        style={{
          backgroundColor: "white",
          maxWidth: "360px",
          transition: "all 0.2s ease",
        }}
      >
        {/* Main Badge */}
        <div
          onClick={() => setIsExpanded(!isExpanded)}
          style={{
            padding: "14px 16px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            borderBottom: isExpanded ? "1px solid #E5E7EB" : "none",
            transition: "all 0.2s ease",
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
            <div style={getLetterGradeStyle(score.overall_rating)}>{score.overall_rating}</div>
            <div>
              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <div
                  style={{
                    fontSize: "16px",
                    fontWeight: 600,
                    color: "#1F2937",
                  }}
                >
                  GoodGoods
                </div>
                {isAnonymous && (
                  <SignInButton
                    size="sm"
                    variant="outline"
                    className="text-xs px-2 py-1"
                    onSignInClick={onSignInClick}
                  />
                )}
              </div>
              {brand && (
                <div
                  style={{
                    fontSize: "14px",
                    color: "#6B7280",
                    marginTop: "2px",
                  }}
                >
                  {brand}
                </div>
              )}
            </div>
          </div>
          <div
            style={{
              color: "#6B7280",
              fontSize: "20px",
              transform: isExpanded ? "rotate(180deg)" : "rotate(0deg)",
              transition: "transform 0.2s ease",
            }}
          >
            ▼
          </div>
        </div>

        {/* Expanded Details */}
        {isExpanded && (
          <div style={{ padding: "16px" }}>
            {/* Tabs */}
            <div
              style={{
                display: "flex",
                borderBottom: "1px solid #E5E7EB",
                marginBottom: "16px",
              }}
            >
              <div
                onClick={() => setActiveTab("details")}
                style={{
                  padding: "8px 16px",
                  fontWeight: activeTab === "details" ? 600 : 400,
                  color: activeTab === "details" ? "#10B981" : "#6B7280",
                  borderBottom: activeTab === "details" ? "2px solid #10B981" : "none",
                  cursor: "pointer",
                }}
              >
                Score Details
              </div>
              {needsAlternatives && (
                <div
                  onClick={() => setActiveTab("alternatives")}
                  style={{
                    padding: "8px 16px",
                    fontWeight: activeTab === "alternatives" ? 600 : 400,
                    color: activeTab === "alternatives" ? "#10B981" : "#6B7280",
                    borderBottom: activeTab === "alternatives" ? "2px solid #10B981" : "none",
                    cursor: "pointer",
                  }}
                >
                  Alternatives
                </div>
              )}
            </div>

            {/* Score Details Tab */}
            {activeTab === "details" && (
              <div>
                {/* Labor Practices */}
                <div style={getDimensionCardStyle(score.labor_practices.rating)}>
                  <div style={{ display: "flex", alignItems: "center", marginBottom: "6px" }}>
                    <span style={{ marginRight: "8px", fontSize: "16px" }}>
                      {dimensionIcons.labor_practices}
                    </span>
                    <h3 style={{ fontWeight: 600, fontSize: "14px", margin: 0 }}>
                      Labor Practices
                    </h3>
                  </div>
                  <p style={{ fontSize: "14px", margin: 0, color: "#4B5563" }}>
                    {score.labor_practices.summary}
                  </p>
                </div>

                {/* Sustainability */}
                <div style={getDimensionCardStyle(score.sustainability.rating)}>
                  <div style={{ display: "flex", alignItems: "center", marginBottom: "6px" }}>
                    <span style={{ marginRight: "8px", fontSize: "16px" }}>
                      {dimensionIcons.sustainability}
                    </span>
                    <h3 style={{ fontWeight: 600, fontSize: "14px", margin: 0 }}>Sustainability</h3>
                  </div>
                  <p style={{ fontSize: "14px", margin: 0, color: "#4B5563" }}>
                    {score.sustainability.summary}
                  </p>
                </div>

                {/* Ethical Sourcing */}
                <div style={getDimensionCardStyle(score.ethical_sourcing.rating)}>
                  <div style={{ display: "flex", alignItems: "center", marginBottom: "6px" }}>
                    <span style={{ marginRight: "8px", fontSize: "16px" }}>
                      {dimensionIcons.ethical_sourcing}
                    </span>
                    <h3 style={{ fontWeight: 600, fontSize: "14px", margin: 0 }}>
                      Ethical Sourcing
                    </h3>
                  </div>
                  <p style={{ fontSize: "14px", margin: 0, color: "#4B5563" }}>
                    {score.ethical_sourcing.summary}
                  </p>
                </div>

                {/* Charitable Donations */}
                <div style={getDimensionCardStyle(score.donations.rating)}>
                  <div style={{ display: "flex", alignItems: "center", marginBottom: "6px" }}>
                    <span style={{ marginRight: "8px", fontSize: "16px" }}>
                      {dimensionIcons.donations}
                    </span>
                    <h3 style={{ fontWeight: 600, fontSize: "14px", margin: 0 }}>
                      Charitable Donations
                    </h3>
                  </div>
                  <p style={{ fontSize: "14px", margin: 0, color: "#4B5563" }}>
                    {score.donations.summary}
                  </p>
                </div>

                {/* Overall Sentiment */}
                <div
                  style={{
                    padding: "12px",
                    backgroundColor: "#F9FAFB",
                    borderRadius: "8px",
                    marginBottom: "12px",
                  }}
                >
                  <div style={{ display: "flex", alignItems: "center", marginBottom: "6px" }}>
                    <span style={{ marginRight: "8px", fontSize: "16px" }}>
                      {dimensionIcons.overall_sentiment}
                    </span>
                    <h3 style={{ fontWeight: 600, fontSize: "14px", margin: 0 }}>
                      Overall Sentiment
                    </h3>
                  </div>
                  <p style={{ fontSize: "14px", margin: 0, color: "#4B5563" }}>
                    {score.overall_sentiment.summary}
                  </p>
                </div>

                {/* Sources */}
                <div style={{ marginTop: "16px" }}>
                  <h4
                    style={{
                      fontSize: "12px",
                      fontWeight: 600,
                      color: "#6B7280",
                      marginBottom: "4px",
                    }}
                  >
                    Sources:
                  </h4>
                  <ul
                    style={{
                      listStyleType: "disc",
                      paddingLeft: "20px",
                      margin: 0,
                    }}
                  >
                    {score.sources.map((source, index) => (
                      <li key={index} style={{ marginBottom: "4px" }}>
                        <a
                          href={source}
                          target="_blank"
                          rel="noopener noreferrer"
                          style={{
                            fontSize: "12px",
                            color: "#3B82F6",
                            textDecoration: "none",
                          }}
                          onMouseOver={(e) => (e.currentTarget.style.textDecoration = "underline")}
                          onMouseOut={(e) => (e.currentTarget.style.textDecoration = "none")}
                        >
                          {source}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {/* Alternatives Tab */}
            {activeTab === "alternatives" && (
              <div>
                {isTopRated ? (
                  <div
                    style={{
                      padding: "16px",
                      backgroundColor: "#ECFDF5",
                      borderRadius: "8px",
                      border: "1px solid #A7F3D0",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        marginBottom: "8px",
                      }}
                    >
                      <span
                        style={{
                          color: "#10B981",
                          marginRight: "8px",
                          fontSize: "18px",
                        }}
                      >
                        ✓
                      </span>
                      <h3
                        style={{
                          fontWeight: 600,
                          color: "#065F46",
                          margin: 0,
                          fontSize: "16px",
                        }}
                      >
                        Ethical Choice
                      </h3>
                    </div>
                    <p
                      style={{
                        fontSize: "14px",
                        color: "#047857",
                        margin: 0,
                      }}
                    >
                      Great job! This brand has an excellent ethical rating. You&apos;re making a
                      positive impact with your purchase.
                    </p>
                  </div>
                ) : (
                  needsAlternatives && (
                    <div>
                      {isLoadingAlternatives ? (
                        <div
                          style={{
                            padding: "16px",
                            textAlign: "center",
                            color: "#6B7280",
                          }}
                        >
                          <div
                            style={{
                              display: "inline-block",
                              width: "24px",
                              height: "24px",
                              border: "3px solid #E5E7EB",
                              borderTopColor: "#10B981",
                              borderRadius: "50%",
                              animation: "spin 1s linear infinite",
                              marginBottom: "8px",
                            }}
                          ></div>
                          <p style={{ fontSize: "14px", margin: 0 }}>
                            Finding ethical alternatives...
                          </p>
                        </div>
                      ) : alternativesError ? (
                        <p
                          style={{
                            fontSize: "14px",
                            color: "#EF4444",
                            padding: "16px",
                            backgroundColor: "#FEF2F2",
                            borderRadius: "8px",
                          }}
                        >
                          {alternativesError}
                        </p>
                      ) : (
                        showAlternativesSection && (
                          <AlternativeProducts
                            products={alternatives}
                            category={productCategory}
                            currentBrand={brand || "Unknown"}
                            isLoading={false}
                            compact={true}
                          />
                        )
                      )}
                    </div>
                  )
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
