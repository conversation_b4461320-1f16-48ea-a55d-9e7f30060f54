import React, { useState } from "react";
import type { EthicalScore, Rating, OverallRating } from "../types";
import { openAlternativesPage } from "../utils/jwt";
import SignInButton from "./Auth/SignInButton";
import { useAuth } from "../contexts/AuthContext";

interface ScoreBadgeProps {
  score: EthicalScore;
  productCategory?: string;
  brand?: string;
  onSignInClick?: () => void;
}

// Updated color palette
const ratingColors: Record<Rating, string> = {
  green: "#10B981", // Emerald
  yellow: "#F59E0B", // Amber
  red: "#EF4444", // Red
};

const ratingBackgroundColors: Record<Rating, string> = {
  green: "#ECFDF5", // Lighter emerald
  yellow: "#FFFBEB", // Lighter amber
  red: "#FEF2F2", // Lighter red
};

// Overall rating colors
const overallRatingColors: Record<OverallRating, string> = {
  A: "#10B981", // Emerald
  B: "#60A5FA", // Blue
  C: "#F59E0B", // Amber
  D: "#EF4444", // Red
};

// Icons for each dimension
const dimensionIcons: Record<string, string> = {
  labor_practices: "👷‍♂️",
  sustainability: "🌱",
  ethical_sourcing: "🔄",
  donations: "💰",
  overall_sentiment: "⭐",
};

export const ScoreBadge: React.FC<ScoreBadgeProps> = ({
  score,
  productCategory = "product",
  brand,
  onSignInClick,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { isAnonymous } = useAuth();

  // Check if we need to show alternatives (score is B or lower)
  const needsAlternatives = ["B", "C", "D"].includes(score.overall_rating);
  const isTopRated = score.overall_rating === "A";

  // Function to handle opening alternatives page
  const handleViewAlternatives = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (brand) {
      await openAlternativesPage(brand, productCategory, score.overall_rating);
    }
  };

  // Helper function to get dimension card style
  const getDimensionCardStyle = (rating: Rating) => ({
    backgroundColor: ratingBackgroundColors[rating],
    borderLeft: `4px solid ${ratingColors[rating]}`,
    borderRadius: "8px",
    padding: "12px",
    marginBottom: "12px",
    boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
  });

  // Get letter grade badge style
  const getLetterGradeStyle = (rating: OverallRating) => ({
    backgroundColor: overallRatingColors[rating],
    color: "white",
    borderRadius: "50%",
    width: "36px",
    height: "36px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    fontWeight: "bold",
    fontSize: "18px",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
  });

  return (
    <div className="fixed top-4 right-4 z-50">
      <div
        className="rounded-xl shadow-lg overflow-hidden cursor-pointer"
        style={{
          backgroundColor: "white",
          maxWidth: "360px",
          transition: "all 0.2s ease",
        }}
      >
        {/* Main Badge */}
        <div
          onClick={() => setIsExpanded(!isExpanded)}
          style={{
            padding: "14px 16px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            borderBottom: isExpanded ? "1px solid #E5E7EB" : "none",
            transition: "all 0.2s ease",
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
            <div style={getLetterGradeStyle(score.overall_rating)}>{score.overall_rating}</div>
            <div>
              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <div
                  style={{
                    fontSize: "16px",
                    fontWeight: 600,
                    color: "#1F2937",
                  }}
                >
                  GoodGoods
                </div>
                {isAnonymous && (
                  <SignInButton
                    size="sm"
                    variant="outline"
                    className="text-xs px-2 py-1"
                    onSignInClick={onSignInClick}
                  />
                )}
              </div>
              {brand && (
                <div
                  style={{
                    fontSize: "14px",
                    color: "#6B7280",
                    marginTop: "2px",
                  }}
                >
                  {brand}
                </div>
              )}
            </div>
          </div>
          <div
            style={{
              color: "#6B7280",
              fontSize: "20px",
              transform: isExpanded ? "rotate(180deg)" : "rotate(0deg)",
              transition: "transform 0.2s ease",
            }}
          >
            ▼
          </div>
        </div>

        {/* Expanded Details */}
        {isExpanded && (
          <div style={{ padding: "16px" }}>
            {/* Score Details */}
            <div>
              {/* Labor Practices */}
              <div style={getDimensionCardStyle(score.labor_practices.rating)}>
                <div style={{ display: "flex", alignItems: "center", marginBottom: "6px" }}>
                  <span style={{ marginRight: "8px", fontSize: "16px" }}>
                    {dimensionIcons.labor_practices}
                  </span>
                  <h3 style={{ fontWeight: 600, fontSize: "14px", margin: 0 }}>Labor Practices</h3>
                </div>
                <p style={{ fontSize: "14px", margin: 0, color: "#4B5563" }}>
                  {score.labor_practices.summary}
                </p>
              </div>

              {/* Sustainability */}
              <div style={getDimensionCardStyle(score.sustainability.rating)}>
                <div style={{ display: "flex", alignItems: "center", marginBottom: "6px" }}>
                  <span style={{ marginRight: "8px", fontSize: "16px" }}>
                    {dimensionIcons.sustainability}
                  </span>
                  <h3 style={{ fontWeight: 600, fontSize: "14px", margin: 0 }}>Sustainability</h3>
                </div>
                <p style={{ fontSize: "14px", margin: 0, color: "#4B5563" }}>
                  {score.sustainability.summary}
                </p>
              </div>

              {/* Ethical Sourcing */}
              <div style={getDimensionCardStyle(score.ethical_sourcing.rating)}>
                <div style={{ display: "flex", alignItems: "center", marginBottom: "6px" }}>
                  <span style={{ marginRight: "8px", fontSize: "16px" }}>
                    {dimensionIcons.ethical_sourcing}
                  </span>
                  <h3 style={{ fontWeight: 600, fontSize: "14px", margin: 0 }}>Ethical Sourcing</h3>
                </div>
                <p style={{ fontSize: "14px", margin: 0, color: "#4B5563" }}>
                  {score.ethical_sourcing.summary}
                </p>
              </div>

              {/* Charitable Donations */}
              <div style={getDimensionCardStyle(score.donations.rating)}>
                <div style={{ display: "flex", alignItems: "center", marginBottom: "6px" }}>
                  <span style={{ marginRight: "8px", fontSize: "16px" }}>
                    {dimensionIcons.donations}
                  </span>
                  <h3 style={{ fontWeight: 600, fontSize: "14px", margin: 0 }}>
                    Charitable Donations
                  </h3>
                </div>
                <p style={{ fontSize: "14px", margin: 0, color: "#4B5563" }}>
                  {score.donations.summary}
                </p>
              </div>

              {/* Overall Sentiment */}
              <div
                style={{
                  padding: "12px",
                  backgroundColor: "#F9FAFB",
                  borderRadius: "8px",
                  marginBottom: "12px",
                }}
              >
                <div style={{ display: "flex", alignItems: "center", marginBottom: "6px" }}>
                  <span style={{ marginRight: "8px", fontSize: "16px" }}>
                    {dimensionIcons.overall_sentiment}
                  </span>
                  <h3 style={{ fontWeight: 600, fontSize: "14px", margin: 0 }}>
                    Overall Sentiment
                  </h3>
                </div>
                <p style={{ fontSize: "14px", margin: 0, color: "#4B5563" }}>
                  {score.overall_sentiment.summary}
                </p>
              </div>

              {/* Sources */}
              <div style={{ marginTop: "16px" }}>
                <h4
                  style={{
                    fontSize: "12px",
                    fontWeight: 600,
                    color: "#6B7280",
                    marginBottom: "4px",
                  }}
                >
                  Sources:
                </h4>
                <ul
                  style={{
                    listStyleType: "disc",
                    paddingLeft: "20px",
                    margin: 0,
                  }}
                >
                  {score.sources.map((source, index) => (
                    <li key={index} style={{ marginBottom: "4px" }}>
                      <a
                        href={source}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{
                          fontSize: "12px",
                          color: "#3B82F6",
                          textDecoration: "none",
                        }}
                        onMouseOver={(e) => (e.currentTarget.style.textDecoration = "underline")}
                        onMouseOut={(e) => (e.currentTarget.style.textDecoration = "none")}
                      >
                        {source}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Alternatives Button */}
              {needsAlternatives && (
                <div style={{ marginTop: "16px" }}>
                  <button
                    onClick={handleViewAlternatives}
                    style={{
                      width: "100%",
                      padding: "12px 16px",
                      backgroundColor: "#3B82F6",
                      color: "white",
                      border: "none",
                      borderRadius: "8px",
                      fontSize: "14px",
                      fontWeight: 600,
                      cursor: "pointer",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      gap: "8px",
                      transition: "background-color 0.2s ease",
                    }}
                    onMouseOver={(e) => (e.currentTarget.style.backgroundColor = "#2563EB")}
                    onMouseOut={(e) => (e.currentTarget.style.backgroundColor = "#3B82F6")}
                  >
                    <span>🔍</span>
                    View Better Alternatives
                  </button>
                  <p
                    style={{
                      fontSize: "12px",
                      color: "#6B7280",
                      textAlign: "center",
                      margin: "8px 0 0 0",
                    }}
                  >
                    Opens in a new tab with ethical alternatives
                  </p>
                </div>
              )}

              {/* Ethical Choice Message for A-rated brands */}
              {isTopRated && (
                <div
                  style={{
                    padding: "16px",
                    backgroundColor: "#ECFDF5",
                    borderRadius: "8px",
                    border: "1px solid #A7F3D0",
                    marginTop: "16px",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      marginBottom: "8px",
                    }}
                  >
                    <span
                      style={{
                        color: "#10B981",
                        marginRight: "8px",
                        fontSize: "18px",
                      }}
                    >
                      ✓
                    </span>
                    <h3
                      style={{
                        fontWeight: 600,
                        color: "#065F46",
                        margin: 0,
                        fontSize: "16px",
                      }}
                    >
                      Ethical Choice
                    </h3>
                  </div>
                  <p
                    style={{
                      fontSize: "14px",
                      color: "#047857",
                      margin: 0,
                    }}
                  >
                    Great job! This brand has an excellent ethical rating. You&apos;re making a
                    positive impact with your purchase.
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
