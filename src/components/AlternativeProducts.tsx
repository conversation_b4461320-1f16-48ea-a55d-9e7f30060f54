import React from "react";
import type { AlternativeProduct, OverallRating } from "../types";

interface AlternativeProductsProps {
  products: AlternativeProduct[];
  category: string;
  currentBrand: string;
  isLoading?: boolean;
  error?: string;
  compact?: boolean;
}

// Overall rating colors
const overallRatingColors: Record<OverallRating, string> = {
  A: "#10B981", // Emerald
  B: "#60A5FA", // Blue
  C: "#F59E0B", // Amber
  D: "#EF4444", // Red
};

export const AlternativeProducts: React.FC<AlternativeProductsProps> = ({
  products,
  category,
  currentBrand,
  isLoading = false,
  error,
  compact = true,
}) => {
  if (isLoading) {
    return (
      <div style={{ padding: compact ? "8px 0" : "16px" }}>
        <h2
          style={{
            fontSize: compact ? "16px" : "18px",
            fontWeight: 700,
            marginBottom: "8px",
          }}
        >
          Finding Better Alternatives...
        </h2>
        <p style={{ fontSize: "14px", color: "#4B5563" }}>
          Searching for ethical alternatives to {currentBrand} {category} products.
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: compact ? "8px 0" : "16px" }}>
        <h2
          style={{
            fontSize: compact ? "16px" : "18px",
            fontWeight: 700,
            marginBottom: "8px",
            color: "#EF4444",
          }}
        >
          Error Finding Alternatives
        </h2>
        <p style={{ fontSize: "14px", color: "#4B5563" }}>{error}</p>
      </div>
    );
  }

  if (!products || products.length === 0) {
    return (
      <div style={{ padding: compact ? "8px 0" : "16px" }}>
        <h2
          style={{
            fontSize: compact ? "16px" : "18px",
            fontWeight: 700,
            marginBottom: "8px",
          }}
        >
          No Alternatives Found
        </h2>
        <p style={{ fontSize: "14px", color: "#4B5563" }}>
          We couldn&apos;t find any ethical alternatives to {currentBrand} {category} products at
          this time.
        </p>
      </div>
    );
  }

  return (
    <div style={{ padding: compact ? "8px 0" : "16px" }}>
      {!compact && (
        <>
          <h2
            style={{
              fontSize: "18px",
              fontWeight: 700,
              marginBottom: "12px",
              color: "#1F2937",
            }}
          >
            Ethical Alternatives to {currentBrand}
          </h2>
          <p
            style={{
              fontSize: "14px",
              marginBottom: "16px",
              color: "#4B5563",
            }}
          >
            Here are some more ethical alternatives in the {category} category:
          </p>
        </>
      )}

      <div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
        {products.slice(0, compact ? 3 : products.length).map((product, index) => (
          <div
            key={index}
            style={{
              border: "1px solid #E5E7EB",
              borderLeft: `4px solid ${overallRatingColors[product.overall_rating]}`,
              borderRadius: "8px",
              padding: "12px",
              transition: "box-shadow 0.2s ease",
              boxShadow: "0 1px 2px rgba(0, 0, 0, 0.05)",
              backgroundColor: "white",
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.boxShadow =
                "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)";
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.boxShadow = "0 1px 2px rgba(0, 0, 0, 0.05)";
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "flex-start",
                marginBottom: "6px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "flex-start",
                  flex: 1,
                }}
              >
                {product.image_url && (
                  <div
                    style={{
                      width: compact ? "40px" : "60px",
                      height: compact ? "40px" : "60px",
                      marginRight: "12px",
                      borderRadius: "4px",
                      overflow: "hidden",
                      flexShrink: 0,
                    }}
                  >
                    <img
                      src={product.image_url}
                      alt={product.name}
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                      onError={(e) => {
                        // Hide the image container if loading fails
                        (e.target as HTMLElement).parentElement!.style.display = "none";
                      }}
                    />
                  </div>
                )}
                <h3
                  style={{
                    fontSize: compact ? "14px" : "16px",
                    fontWeight: 600,
                    margin: 0,
                    color: "#1F2937",
                    flex: 1,
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  }}
                >
                  {compact && product.name.length > 30
                    ? `${product.name.substring(0, 30)}...`
                    : product.name}
                </h3>
              </div>
              <span
                style={{
                  backgroundColor: `${overallRatingColors[product.overall_rating]}20`, // 20% opacity
                  color: overallRatingColors[product.overall_rating],
                  fontSize: "12px",
                  fontWeight: 600,
                  padding: "2px 8px",
                  borderRadius: "12px",
                  marginLeft: "8px",
                  display: "inline-block",
                  flexShrink: 0,
                }}
              >
                {product.overall_rating} Rating
              </span>
            </div>

            <p
              style={{
                fontSize: "12px",
                color: "#6B7280",
                margin: "4px 0",
              }}
            >
              Brand: <span style={{ fontWeight: 500 }}>{product.brand}</span>
            </p>

            {product.price && (
              <p
                style={{
                  fontSize: "14px",
                  fontWeight: 600,
                  color: "#10B981",
                  margin: "8px 0 4px 0",
                }}
              >
                {product.price}
              </p>
            )}

            {!compact && (
              <p
                style={{
                  fontSize: "14px",
                  margin: "8px 0",
                  color: "#4B5563",
                  lineHeight: "1.4",
                }}
              >
                {product.description}
              </p>
            )}

            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginTop: "8px",
              }}
            >
              <p
                style={{
                  fontSize: "12px",
                  color: "#047857",
                  margin: 0,
                  flex: 1,
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                {compact ? (
                  <>
                    <span style={{ fontWeight: 600 }}>Why better:</span>{" "}
                    {product.reason.length > 50
                      ? `${product.reason.substring(0, 50)}...`
                      : product.reason}
                  </>
                ) : (
                  <>
                    <span style={{ fontWeight: 600 }}>Why it&apos;s better:</span> {product.reason}
                  </>
                )}
              </p>

              {(product.search_url || product.url) && (
                <a
                  href={product.search_url || product.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    fontSize: "12px",
                    backgroundColor: "#3B82F6",
                    color: "white",
                    padding: "4px 10px",
                    borderRadius: "4px",
                    textDecoration: "none",
                    fontWeight: 500,
                    marginLeft: "8px",
                    display: "inline-block",
                    transition: "background-color 0.2s ease",
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.backgroundColor = "#2563EB";
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.backgroundColor = "#3B82F6";
                  }}
                >
                  {product.search_url ? "Shop Online" : "View Product"}
                </a>
              )}
            </div>
          </div>
        ))}

        {compact && products.length > 3 && (
          <p
            style={{
              fontSize: "12px",
              color: "#6B7280",
              textAlign: "center",
              margin: "4px 0 0 0",
            }}
          >
            + {products.length - 3} more alternatives available
          </p>
        )}
      </div>
    </div>
  );
};
