// Security utilities for token generation and validation
// Note: This is a simplified JWT implementation for Chrome extension use
// In a production environment, consider using a proper JWT library

interface TokenPayload {
  brand: string;
  category: string;
  rating: string;
  timestamp: number;
  exp: number; // Expiration timestamp
}

// Shared secret key - in production, this should be more secure
const SECRET_KEY = "goodgoods-alternatives-secret-2024";

// Simple base64 encoding/decoding
function base64UrlEncode(str: string): string {
  return btoa(str)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

function base64UrlDecode(str: string): string {
  // Add padding if needed
  str += '='.repeat((4 - str.length % 4) % 4);
  return atob(str.replace(/-/g, '+').replace(/_/g, '/'));
}

// Simple HMAC-SHA256 implementation using Web Crypto API
async function hmacSha256(key: string, data: string): Promise<string> {
  const encoder = new TextEncoder();
  const keyData = encoder.encode(key);
  const messageData = encoder.encode(data);
  
  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  
  const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageData);
  const hashArray = Array.from(new Uint8Array(signature));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  return hashHex;
}

// Generate a secure token for alternatives page access
export async function generateAlternativesToken(
  brand: string,
  category: string,
  rating: string
): Promise<string> {
  const now = Date.now();
  const payload: TokenPayload = {
    brand,
    category,
    rating,
    timestamp: now,
    exp: now + (30 * 60 * 1000), // 30 minutes expiration
  };

  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  const encodedHeader = base64UrlEncode(JSON.stringify(header));
  const encodedPayload = base64UrlEncode(JSON.stringify(payload));
  
  const data = `${encodedHeader}.${encodedPayload}`;
  const signature = await hmacSha256(SECRET_KEY, data);
  const encodedSignature = base64UrlEncode(signature);
  
  return `${data}.${encodedSignature}`;
}

// Validate and decode a token
export async function validateAlternativesToken(token: string): Promise<TokenPayload | null> {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.error('Invalid token format');
      return null;
    }

    const [encodedHeader, encodedPayload, encodedSignature] = parts;
    
    // Verify signature
    const data = `${encodedHeader}.${encodedPayload}`;
    const expectedSignature = await hmacSha256(SECRET_KEY, data);
    const expectedEncodedSignature = base64UrlEncode(expectedSignature);
    
    if (encodedSignature !== expectedEncodedSignature) {
      console.error('Invalid token signature');
      return null;
    }

    // Decode payload
    const payloadJson = base64UrlDecode(encodedPayload);
    const payload: TokenPayload = JSON.parse(payloadJson);
    
    // Check expiration
    if (Date.now() > payload.exp) {
      console.error('Token has expired');
      return null;
    }

    return payload;
  } catch (error) {
    console.error('Error validating token:', error);
    return null;
  }
}

// Generate the alternatives page URL with token
export async function generateAlternativesUrl(
  brand: string,
  category: string,
  rating: string
): Promise<string> {
  const token = await generateAlternativesToken(brand, category, rating);
  const baseUrl = chrome.runtime.getURL('alternatives.html');
  return `${baseUrl}?token=${encodeURIComponent(token)}`;
}
