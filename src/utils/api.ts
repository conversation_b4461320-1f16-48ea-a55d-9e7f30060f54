import { httpsCallable } from "firebase/functions";
import { functions } from "../firebase";

// Type definitions for our API
type ChatMessage = {
  role: "user" | "assistant" | "system";
  content: string;
};

type ChatCompletionParams = {
  messages: ChatMessage[];
  model?: string;
  max_tokens?: number;
  temperature?: number;
  metadata?: {
    source: string;
    extensionId: string;
  };
};

// Helper function to get the current Chrome extension context
const getExtensionContext = () => {
  return typeof chrome !== "undefined" && chrome.runtime && chrome.runtime.id;
};

export const chatCompletion = async (params: ChatCompletionParams) => {
  try {
    // In a Chrome extension, we might not have a traditional user ID
    // You might want to implement your own auth system or use Chrome's identity API
    const extensionId = getExtensionContext();

    if (!extensionId) {
      throw new Error("Extension context not available");
    }

    // Call the Firebase Function
    const chatFunction = httpsCallable<ChatCompletionParams, any>(functions, "chatCompletion");
    const result = await chatFunction({
      ...params,
      model: params.model || "gpt-4",
      max_tokens: params.max_tokens || 1000,
      temperature: params.temperature ?? 0.7,
      // Include extension ID for tracking/validation
      metadata: {
        source: "chrome-extension",
        extensionId,
      },
    });

    return result.data;
  } catch (error) {
    console.error("Error in chatCompletion:", error);
    // Re-throw the error with additional context
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    throw new Error(`Failed to get chat completion: ${errorMessage}`);
    throw error;
  }
};

// Helper function to get the current user's ID
// You'll need to implement this based on your authentication setup
const getCurrentUserId = async (): Promise<string | null> => {
  // Example: Get user ID from Chrome's storage
  return new Promise((resolve) => {
    if (chrome?.storage?.sync) {
      chrome.storage.sync.get(["userId"], (result) => {
        resolve(result.userId || null);
      });
    } else {
      // Fallback for development
      resolve("dev-user-" + Math.random().toString(36).substring(2, 9));
    }
  });
};

// Health check function
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    const response = await fetch(
      "https://us-central1-<your-project-id>.cloudfunctions.net/healthCheck"
    );
    const data = await response.json();
    return data.status === "ok";
  } catch (error) {
    console.error("Health check failed:", error);
    return false;
  }
};
