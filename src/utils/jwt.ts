// Simple JWT implementation for browser environment
// This should be the same secret used in the Next.js app
const JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-key-change-in-production";

export interface AlternativesTokenPayload {
  brand: string;
  category: string;
  currentRating: string;
  timestamp: number;
  exp: number; // Expiration time
}

/**
 * Base64 URL encode
 */
function base64UrlEncode(str: string): string {
  return btoa(str).replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
}

/**
 * Simple HMAC SHA256 implementation using Web Crypto API
 */
async function hmacSha256(key: string, data: string): Promise<string> {
  const encoder = new TextEncoder();
  const keyData = encoder.encode(key);
  const messageData = encoder.encode(data);

  const cryptoKey = await crypto.subtle.importKey(
    "raw",
    keyData,
    { name: "HM<PERSON>", hash: "SHA-256" },
    false,
    ["sign"]
  );

  const signature = await crypto.subtle.sign("HMAC", cryptoKey, messageData);
  const signatureArray = new Uint8Array(signature);
  const signatureString = String.fromCharCode(...signatureArray);

  return base64UrlEncode(signatureString);
}

/**
 * Generate a simple JWT token for accessing the alternatives page
 * @param brand - The current brand name
 * @param category - The product category
 * @param currentRating - The current brand's rating (A, B, C, D)
 * @returns Signed JWT token
 */
export async function generateAlternativesToken(
  brand: string,
  category: string,
  currentRating: string
): Promise<string> {
  const now = Math.floor(Date.now() / 1000);
  const payload: AlternativesTokenPayload = {
    brand,
    category,
    currentRating,
    timestamp: now,
    exp: now + 60 * 15, // Token expires in 15 minutes
  };

  const header = {
    alg: "HS256",
    typ: "JWT",
  };

  const encodedHeader = base64UrlEncode(JSON.stringify(header));
  const encodedPayload = base64UrlEncode(JSON.stringify(payload));
  const data = `${encodedHeader}.${encodedPayload}`;

  const signature = await hmacSha256(JWT_SECRET, data);

  return `${data}.${signature}`;
}

/**
 * Generate the URL for the alternatives page with the secure token
 * @param brand - The current brand name
 * @param category - The product category
 * @param currentRating - The current brand's rating (A, B, C, D)
 * @returns Complete URL for the alternatives page
 */
export async function generateAlternativesUrl(
  brand: string,
  category: string,
  currentRating: string
): Promise<string> {
  const token = await generateAlternativesToken(brand, category, currentRating);

  // For development, always use localhost. For production, use environment variable or default
  const baseUrl = process.env.ALTERNATIVES_URL || "http://localhost:3000";

  return `${baseUrl}/alternatives?token=${encodeURIComponent(token)}`;
}

/**
 * Open the alternatives page in a new tab
 * @param brand - The current brand name
 * @param category - The product category
 * @param currentRating - The current brand's rating (A, B, C, D)
 */
export async function openAlternativesPage(
  brand: string,
  category: string,
  currentRating: string
): Promise<void> {
  const url = await generateAlternativesUrl(brand, category, currentRating);

  // Use Chrome extension API to open new tab
  if (typeof chrome !== "undefined" && chrome.tabs) {
    chrome.tabs.create({ url });
  } else {
    // Fallback for development/testing
    window.open(url, "_blank");
  }
}
