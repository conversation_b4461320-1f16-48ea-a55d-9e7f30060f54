import jwt from "jsonwebtoken";

// This should be the same secret used in the Next.js app
// In production, this would be loaded from a secure configuration
const JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-key-change-in-production";

export interface AlternativesTokenPayload {
  brand: string;
  category: string;
  currentRating: string;
  timestamp: number;
  exp: number; // Expiration time
}

/**
 * Generate a JWT token for accessing the alternatives page
 * @param brand - The current brand name
 * @param category - The product category
 * @param currentRating - The current brand's rating (A, B, C, D)
 * @returns Signed JWT token
 */
export function generateAlternativesToken(
  brand: string,
  category: string,
  currentRating: string
): string {
  const now = Math.floor(Date.now() / 1000);
  const payload: AlternativesTokenPayload = {
    brand,
    category,
    currentRating,
    timestamp: now,
    exp: now + 60 * 15, // Token expires in 15 minutes
  };

  return jwt.sign(payload, JWT_SECRET);
}

/**
 * Generate the URL for the alternatives page with the secure token
 * @param brand - The current brand name
 * @param category - The product category
 * @param currentRating - The current brand's rating (A, B, C, D)
 * @returns Complete URL for the alternatives page
 */
export function generateAlternativesUrl(
  brand: string,
  category: string,
  currentRating: string
): string {
  const token = generateAlternativesToken(brand, category, currentRating);

  // In development, use localhost. In production, use the Vercel URL
  const baseUrl =
    process.env.NODE_ENV === "development"
      ? "http://localhost:3000"
      : "https://your-app-name.vercel.app"; // This will be updated with actual Vercel URL

  return `${baseUrl}/alternatives?token=${encodeURIComponent(token)}`;
}

/**
 * Open the alternatives page in a new tab
 * @param brand - The current brand name
 * @param category - The product category
 * @param currentRating - The current brand's rating (A, B, C, D)
 */
export function openAlternativesPage(brand: string, category: string, currentRating: string): void {
  const url = generateAlternativesUrl(brand, category, currentRating);

  // Use Chrome extension API to open new tab
  if (typeof chrome !== "undefined" && chrome.tabs) {
    chrome.tabs.create({ url });
  } else {
    // Fallback for development/testing
    window.open(url, "_blank");
  }
}
