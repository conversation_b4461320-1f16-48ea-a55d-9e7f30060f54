import { BrandDetectionResult } from "../types";

export function detectBrand(): BrandDetectionResult | null {
  console.log("GoodGoods: Attempting to detect brand");

  // Try multiple methods to find the brand, starting with the most reliable
  const methods = [
    detectBrandFromBrandElement,
    detectBrand<PERSON>rom<PERSON><PERSON>ine,
    detectBrand<PERSON>romTitle,
    detectBrandFromBreadcrumbs,
    detectBrandFromUrl, // Fallback method
  ];

  for (const method of methods) {
    try {
      const result = method();
      if (result) {
        console.log(
          `GoodGoods: Brand detected: "${result.brand}" (confidence: ${result.confidence})`
        );
        return result;
      }
    } catch (error) {
      console.error("GoodGoods: Error in brand detection method:", error);
      // Continue to the next method if one fails
    }
  }

  // Last resort fallback - use a generic brand name
  console.log("GoodGoods: Could not detect brand using any method, using fallback");
  return {
    brand: "Amazon Product",
    confidence: 0.1,
  };
}

// Method 1: Try to find brand from dedicated brand element
function detectBrandFromBrandElement(): BrandDetectionResult | null {
  try {
    // Try various selectors that might contain the brand
    const selectors = [
      ".po-brand .a-span9",
      "#bylineInfo_feature_div .a-link-normal",
      ".a-section.a-spacing-none.a-spacing-top-small .a-row .a-size-base",
      '[data-feature-name="brandByline"] .a-link-normal',
      "#productOverview_feature_div .a-section .a-row:first-child .a-span9",
    ];

    for (const selector of selectors) {
      try {
        const element = document.querySelector(selector);
        if (element) {
          const text = element.textContent?.trim();
          if (text && text.length > 0 && text.length < 50) {
            // Reasonable brand name length
            return {
              brand: text.replace(/^visit the |^brand: /i, "").trim(),
              confidence: 0.95,
            };
          }
        }
      } catch (selectorError) {
        console.error(`GoodGoods: Error with selector ${selector}:`, selectorError);
        // Continue to next selector
      }
    }

    return null;
  } catch (error) {
    console.error("GoodGoods: Error in detectBrandFromBrandElement:", error);
    return null;
  }
}

// Method 2: Try to find brand from the "By [Brand]" or "Visit the [Brand] Store" text
function detectBrandFromByline(): BrandDetectionResult | null {
  try {
    const byBrandElement = document.querySelector("#bylineInfo");
    if (byBrandElement) {
      const text = byBrandElement.textContent || "";

      // Pattern for "by Brand" or "by Brand |" format
      try {
        const byMatch = text.match(/by\s+([^|]+?)(?:\s*\||$)/i);
        if (byMatch && byMatch[1]) {
          return {
            brand: byMatch[1].trim(),
            confidence: 0.9,
          };
        }
      } catch (regexError) {
        console.error("GoodGoods: Error in byline regex:", regexError);
      }

      // Pattern for "Visit the Brand Store" format
      try {
        const visitMatch = text.match(/visit the\s+([^\s]+)\s+store/i);
        if (visitMatch && visitMatch[1]) {
          return {
            brand: visitMatch[1].trim(),
            confidence: 0.9,
          };
        }
      } catch (regexError) {
        console.error("GoodGoods: Error in visit store regex:", regexError);
      }

      // Pattern for "Brand" only format
      try {
        const brandOnlyMatch = text.match(/^([^|]+?)(?:\s*\||$)/i);
        if (brandOnlyMatch && brandOnlyMatch[1] && !text.toLowerCase().includes("by")) {
          return {
            brand: brandOnlyMatch[1].trim(),
            confidence: 0.85,
          };
        }
      } catch (regexError) {
        console.error("GoodGoods: Error in brand only regex:", regexError);
      }
    }

    return null;
  } catch (error) {
    console.error("GoodGoods: Error in detectBrandFromByline:", error);
    return null;
  }
}

// Method 3: Try to find brand from the product title
function detectBrandFromTitle(): BrandDetectionResult | null {
  try {
    const titleElement = document.querySelector("#productTitle");
    if (titleElement) {
      const text = titleElement.textContent?.trim() || "";

      // Look for brand in beginning of title (common pattern)
      // Brand is often the first word or phrase before a delimiter
      try {
        const match = text.match(/^([^-|,]+?)(?:\s*[-|,]|$)/);
        if (match && match[1] && match[1].trim().length > 0 && match[1].trim().length < 30) {
          return {
            brand: match[1].trim(),
            confidence: 0.7,
          };
        }
      } catch (regexError) {
        console.error("GoodGoods: Error in title regex:", regexError);
      }
    }

    return null;
  } catch (error) {
    console.error("GoodGoods: Error in detectBrandFromTitle:", error);
    return null;
  }
}

// Method 4: Try to find brand from breadcrumbs
function detectBrandFromBreadcrumbs(): BrandDetectionResult | null {
  try {
    const breadcrumbs = document.querySelectorAll(
      "#wayfinding-breadcrumbs_feature_div .a-link-normal"
    );
    if (breadcrumbs && breadcrumbs.length > 0) {
      // The last breadcrumb is often the brand
      try {
        const lastBreadcrumb = breadcrumbs[breadcrumbs.length - 1];
        const text = lastBreadcrumb.textContent?.trim();
        if (text && text.length > 0 && text.length < 30) {
          return {
            brand: text,
            confidence: 0.6,
          };
        }
      } catch (elementError) {
        console.error("GoodGoods: Error accessing breadcrumb element:", elementError);
      }
    }

    return null;
  } catch (error) {
    console.error("GoodGoods: Error in detectBrandFromBreadcrumbs:", error);
    return null;
  }
}

// Method 5: Try to extract brand from URL
function detectBrandFromUrl(): BrandDetectionResult | null {
  try {
    // Get the current URL
    const url = window.location.href;
    console.log("GoodGoods: Trying to extract brand from URL:", url);

    // Try to extract brand from URL patterns

    // Pattern 1: /stores/BRAND/ in URL
    const storeMatch = url.match(/\/stores\/([^\/]+)/);
    if (storeMatch && storeMatch[1]) {
      const brand = decodeURIComponent(storeMatch[1]).replace(/-/g, " ");
      return {
        brand: brand,
        confidence: 0.8,
      };
    }

    // Pattern 2: /brands/BRAND/ in URL
    const brandMatch = url.match(/\/brands\/([^\/]+)/);
    if (brandMatch && brandMatch[1]) {
      const brand = decodeURIComponent(brandMatch[1]).replace(/-/g, " ");
      return {
        brand: brand,
        confidence: 0.8,
      };
    }

    // Pattern 3: Extract from product ID in URL
    // This is a very low confidence method, but better than nothing
    const productIdMatch = url.match(/\/dp\/([A-Z0-9]{10})/);
    if (productIdMatch && productIdMatch[1]) {
      // Use the product ID as a unique identifier
      return {
        brand: `Product ${productIdMatch[1].substring(0, 5)}`,
        confidence: 0.2,
      };
    }

    return null;
  } catch (error) {
    console.error("GoodGoods: Error in detectBrandFromUrl:", error);
    return null;
  }
}
