import { EthicalScore, AlternativeProduct, OverallRating, Rating } from "../types";

// Helper function to convert 1-5 rating to color (used in mock data)
function convertToColor(score: number): Rating {
  if (score >= 4) return "green";
  if (score >= 2.5) return "yellow";
  return "red";
}

// Mock implementation for development/testing
function getMockEthicalScore(brand: string): EthicalScore {
  console.log("GoodGoods: Using mock ethical score for", brand);
  return {
    labor_practices: {
      summary: "Limited information available about labor practices.",
      rating: "yellow",
    },
    sustainability: {
      summary: "Some sustainability initiatives reported.",
      rating: "yellow",
    },
    ethical_sourcing: {
      summary: "Sourcing practices not well documented.",
      rating: "yellow",
    },
    donations: {
      summary: "No significant charitable contributions found.",
      rating: "red",
    },
    overall_sentiment: {
      summary: "Mixed reviews on ethical practices.",
    },
    overall_rating: "C",
    sources: [],
  };
}

export async function getEthicalScore(brand: string): Promise<EthicalScore> {
  // Use Firebase function URL directly
  const functionUrl =
    process.env.NODE_ENV === "development"
      ? "http://localhost:5001/swai-3136c/us-central1/getEthicalScore"
      : "https://us-central1-swai-3136c.cloudfunctions.net/getEthicalScore";

  try {
    // Call the API endpoint
    const response = await fetch(functionUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ brand }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Failed to get ethical score");
    }

    return await response.json();
  } catch (error) {
    console.error("Error in getEthicalScore:", error);
    // Fall back to mock data in case of error
    return getMockEthicalScore(brand);
  }
}

// Mock implementation for alternative products
function getMockAlternativeProducts(category: string, currentBrand: string): AlternativeProduct[] {
  console.log("GoodGoods: Using mock alternative products for", category, "from", currentBrand);
  return [
    {
      name: "Example Product",
      brand: "Example Brand",
      description: "A sustainable alternative product.",
      price: "$29.99",
      search_url: "https://www.google.com/search?q=example+brand+product&tbm=shop",
      image_url: "https://example.com/image.jpg",
      overall_rating: "A",
      reason: "Better sustainability practices and ethical sourcing.",
    },
  ];
}

export async function getAlternativeProducts(
  category: string,
  currentBrand: string,
  currentRating: OverallRating
): Promise<AlternativeProduct[]> {
  try {
    const functionUrl =
      process.env.NODE_ENV === "development"
        ? "http://localhost:5001/swai-3136c/us-central1/getAlternativeProducts"
        : "https://us-central1-swai-3136c.cloudfunctions.net/getAlternativeProducts";
    // Call the API endpoint

    const response = await fetch(functionUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        category,
        currentBrand,
        currentRating,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Failed to get alternative products");
    }

    const data = await response.json();

    // Map to the expected AlternativeProduct type with fallbacks
    return data.map((alt: any) => ({
      name: alt.name || "Unknown Product",
      brand: alt.brand || "Unknown Brand",
      description: alt.description || "No description available.",
      price: alt.price || "$$",
      search_url:
        alt.search_url ||
        `https://www.google.com/search?q=${encodeURIComponent((alt.brand || "") + " " + (alt.name || ""))}&tbm=shop`,
      image_url: alt.image_url || "",
      overall_rating: ["A", "B", "C", "D"].includes(alt.overall_rating) ? alt.overall_rating : "B",
      reason: alt.reason || "This is an ethical alternative.",
    }));
  } catch (error) {
    console.error("Error in getAlternativeProducts:", error);
    // Fall back to mock data in case of error
    return getMockAlternativeProducts(category, currentBrand);
  }
}
