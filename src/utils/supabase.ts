import { createClient, SupabaseClient } from "@supabase/supabase-js";
import type { CachedScore, OverallRating, AlternativeProduct, CachedAlternatives } from "../types";

// We'll initialize the Supabase client with credentials from chrome.storage or environment variables
let supabaseClient: SupabaseClient | null = null;

// Get or create the Supabase client
async function getSupabaseClient(): Promise<SupabaseClient | null> {
  // If we already have a client, return it
  if (supabaseClient) {
    return supabaseClient;
  }

  try {
    let supabaseUrl = "";
    let supabaseKey = "";

    // First try to get credentials from chrome.storage.local
    try {
      const result = await chrome.storage.local.get(["SUPABASE_URL", "SUPABASE_ANON_KEY"]);
      supabaseUrl = result.SUPABASE_URL || "";
      supabaseKey = result.SUPABASE_ANON_KEY || "";

      if (supabaseUrl && supabaseKey) {
        console.log("GoodGoods: Supabase credentials loaded from storage");
      }
    } catch (storageError) {
      console.log("GoodGoods: Could not access chrome.storage, will try environment variable");
    }

    // Fallback to environment variable (for webpack injection)
    if (!supabaseUrl || !supabaseKey) {
      supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || "";
      supabaseKey =
        process.env.SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "";
      if (supabaseUrl && supabaseKey) {
        console.log("GoodGoods: Supabase credentials loaded from env");
        // Store them in chrome.storage for future use if possible
        try {
          await chrome.storage.local.set({
            SUPABASE_URL: supabaseUrl,
            SUPABASE_ANON_KEY: supabaseKey,
            NEXT_PUBLIC_SUPABASE_URL: supabaseUrl,
            NEXT_PUBLIC_SUPABASE_ANON_KEY: supabaseKey,
          });
          console.log("GoodGoods: Stored Supabase credentials from env to chrome.storage");
        } catch (storageError) {
          console.log("GoodGoods: Could not store Supabase credentials to chrome.storage");
        }
      }
    }

    // Check if we have valid credentials from either source
    if (!supabaseUrl || !supabaseKey) {
      console.log("GoodGoods: Missing Supabase credentials from both storage and env");
      return null;
    }

    // Create and store the client with credentials
    supabaseClient = createClient(supabaseUrl, supabaseKey);
    console.log("GoodGoods: Supabase client initialized");
    return supabaseClient;
  } catch (error) {
    console.error(
      "GoodGoods: Error initializing Supabase client:",
      error instanceof Error ? error.message : JSON.stringify(error)
    );
    return null;
  }
}

// Mock implementation for development/testing
async function mockCache(brand: string): Promise<CachedScore | null> {
  console.log("GoodGoods: Using mock cache for", brand);

  // Simulate a cache miss for testing the Perplexity API flow
  return null;
}

// Function to convert data from the database to the expected CachedScore format
function convertToCachedScore(data: Record<string, any>): CachedScore {
  try {
    // Check if the data is already in the expected format
    if (
      data.labor_practices &&
      data.sustainability &&
      data.ethical_sourcing &&
      data.donations &&
      data.overall_sentiment
    ) {
      // Data is already in the expected format
      return data as CachedScore;
    }

    // Check if we have the data stored as a JSON string in score_data
    if (data.score_data) {
      try {
        // Try to parse the JSON string
        const parsedData =
          typeof data.score_data === "string" ? JSON.parse(data.score_data) : data.score_data;

        // Check if the parsed data has the expected structure
        if (
          parsedData.labor_practices &&
          parsedData.sustainability &&
          parsedData.ethical_sourcing &&
          parsedData.donations &&
          parsedData.overall_sentiment
        ) {
          // Make sure the brand and last_updated fields are set correctly
          const result = {
            ...parsedData,
            brand: data.brand || parsedData.brand,
            last_updated: data.last_updated || parsedData.last_updated || new Date().toISOString(),
          };

          return result as CachedScore;
        }
      } catch (parseError) {
        console.error("GoodGoods: Error parsing score_data:", parseError);
      }
    }

    // Convert numeric score to letter grade if we have it
    let overallRating: OverallRating = "B"; // Default
    if (typeof data.score === "number") {
      if (data.score >= 3.5) {
        overallRating = "A";
      } else if (data.score >= 2.5) {
        overallRating = "B";
      } else if (data.score >= 1.5) {
        overallRating = "C";
      } else {
        overallRating = "D";
      }
    } else if (typeof data.score === "string" && data.score.length === 1) {
      // If it's a string like 'A', 'B', etc.
      overallRating = data.score as OverallRating;
    } else if (data.overall_rating) {
      overallRating = data.overall_rating as OverallRating;
    }

    // Try to parse sources if they're stored as a JSON string
    let sources: string[] = [];
    if (data.sources) {
      if (Array.isArray(data.sources)) {
        sources = data.sources;
      } else if (typeof data.sources === "string") {
        try {
          const parsedSources = JSON.parse(data.sources);
          if (Array.isArray(parsedSources)) {
            sources = parsedSources;
          }
        } catch (parseError) {
          console.error("GoodGoods: Error parsing sources:", parseError);
        }
      }
    }

    // Otherwise, try to reconstruct the expected format from flattened fields
    const cachedScore: CachedScore = {
      brand: data.brand,
      labor_practices: {
        summary: data.labor_practices_summary || "No information available",
        rating: (data.labor_practices_rating as any) || "yellow",
      },
      sustainability: {
        summary: data.sustainability_summary || "No information available",
        rating: (data.sustainability_rating as any) || "yellow",
      },
      ethical_sourcing: {
        summary: data.ethical_sourcing_summary || "No information available",
        rating: (data.ethical_sourcing_rating as any) || "yellow",
      },
      donations: {
        summary: data.donations_summary || "No information available",
        rating: (data.donations_rating as any) || "yellow",
      },
      overall_sentiment: {
        summary: data.overall_sentiment_summary || "No detailed information available",
      },
      overall_rating: overallRating,
      sources: sources,
      last_updated: data.last_updated || new Date().toISOString(),
    };

    return cachedScore;
  } catch (error) {
    console.error("GoodGoods: Error converting data to CachedScore:", error);

    // Convert numeric score to letter grade if we have it
    let overallRating: OverallRating = "B"; // Default
    if (typeof data.score === "number") {
      if (data.score >= 3.5) {
        overallRating = "A";
      } else if (data.score >= 2.5) {
        overallRating = "B";
      } else if (data.score >= 1.5) {
        overallRating = "C";
      } else {
        overallRating = "D";
      }
    }

    // Return a minimal valid CachedScore
    return {
      brand: data.brand || "Unknown",
      labor_practices: { summary: "No information available", rating: "yellow" },
      sustainability: { summary: "No information available", rating: "yellow" },
      ethical_sourcing: { summary: "No information available", rating: "yellow" },
      donations: { summary: "No information available", rating: "yellow" },
      overall_sentiment: { summary: "No detailed information available" },
      overall_rating: overallRating,
      sources: [],
      last_updated: new Date().toISOString(),
    };
  }
}

export async function getScoreFromCache(brand: string): Promise<CachedScore | null> {
  try {
    // Try to get the Supabase client
    const supabase = await getSupabaseClient();

    // If we don't have valid credentials, use mock implementation
    if (!supabase) {
      console.log("GoodGoods: No Supabase credentials, using mock cache");
      return mockCache(brand);
    }

    console.log("GoodGoods: Querying cache for", brand);
    const { data, error } = await supabase
      .from("brand_scores")
      .select("*")
      .eq("brand", brand)
      .maybeSingle(); // Use maybeSingle instead of single to avoid error when no rows found

    if (error) {
      console.error("GoodGoods: Supabase error:", JSON.stringify(error));
      return null;
    }

    if (!data) {
      console.log("GoodGoods: No data found in cache");
      return null;
    }

    // Check if cache is expired (30 days)
    const lastUpdated = new Date(data.last_updated);
    const now = new Date();
    const daysSinceUpdate = (now.getTime() - lastUpdated.getTime()) / (1000 * 60 * 60 * 24);

    if (daysSinceUpdate > 30) {
      console.log("GoodGoods: Cache expired");
      return null;
    }

    console.log("GoodGoods: Found in cache");

    // Convert the data to the expected format
    const cachedScore = convertToCachedScore(data);
    return cachedScore;
  } catch (error) {
    console.error(
      "GoodGoods: Error accessing cache:",
      error instanceof Error ? error.message : JSON.stringify(error)
    );
    return null;
  }
}

export async function saveScoreToCache(score: CachedScore): Promise<void> {
  try {
    // Try to get the Supabase client
    const supabase = await getSupabaseClient();

    // If we don't have valid credentials, just log and return
    if (!supabase) {
      console.log("GoodGoods: No Supabase credentials, skipping cache save");
      return;
    }

    console.log("GoodGoods: Saving to cache:", score.brand);

    // Convert the overall_rating to a numeric score
    let numericScore: number;
    switch (score.overall_rating) {
      case "A":
        numericScore = 4.0;
        break;
      case "B":
        numericScore = 3.0;
        break;
      case "C":
        numericScore = 2.0;
        break;
      case "D":
        numericScore = 1.0;
        break;
      default:
        numericScore = 2.5; // Default to middle value
    }

    // Store all the data from Perplexity API
    try {
      // First, try to save with all the data in a JSON field
      const completeScoreData = {
        brand: score.brand,
        score: numericScore,
        last_updated: score.last_updated || new Date().toISOString(),
        // Store all the detailed data in a JSON field
        score_data: JSON.stringify(score),
        // Also store individual fields for easier querying
        labor_practices_summary: score.labor_practices.summary,
        labor_practices_rating: score.labor_practices.rating,
        sustainability_summary: score.sustainability.summary,
        sustainability_rating: score.sustainability.rating,
        ethical_sourcing_summary: score.ethical_sourcing.summary,
        ethical_sourcing_rating: score.ethical_sourcing.rating,
        donations_summary: score.donations.summary,
        donations_rating: score.donations.rating,
        overall_sentiment_summary: score.overall_sentiment.summary,
        overall_rating: score.overall_rating,
        sources: JSON.stringify(score.sources),
      };

      console.log("GoodGoods: Trying to save complete score data");
      const { error } = await supabase.from("brand_scores").upsert([completeScoreData]);

      if (!error) {
        console.log("GoodGoods: Successfully saved complete score data to cache");
        return;
      }

      console.error("GoodGoods: Error saving complete score data:", JSON.stringify(error));

      // If that fails, try with just the score_data JSON field
      const scoreWithJsonData = {
        brand: score.brand,
        score: numericScore,
        last_updated: score.last_updated || new Date().toISOString(),
        score_data: JSON.stringify(score),
      };

      console.log("GoodGoods: Trying with score_data JSON field");
      const { error: jsonError } = await supabase.from("brand_scores").upsert([scoreWithJsonData]);

      if (!jsonError) {
        console.log("GoodGoods: Successfully saved to cache with score_data JSON field");
        return;
      }

      console.error(
        "GoodGoods: Error saving with score_data JSON field:",
        JSON.stringify(jsonError)
      );

      // If that fails too, fall back to the minimal required fields
      const minimalRequiredScore = {
        brand: score.brand,
        score: numericScore,
        last_updated: new Date().toISOString(),
      };

      console.log("GoodGoods: Trying with minimal required fields");
      const { error: minimalError } = await supabase
        .from("brand_scores")
        .upsert([minimalRequiredScore]);

      if (!minimalError) {
        console.log("GoodGoods: Successfully saved to cache with minimal required fields");
        return;
      }

      console.error(
        "GoodGoods: Error saving with minimal required fields:",
        JSON.stringify(minimalError)
      );

      // If all else fails, log a helpful message about the database schema
      console.log(
        "GoodGoods: All save attempts failed. The database schema may need to be updated."
      );
      console.log(
        "GoodGoods: Consider adding a score_data JSONB column to the brand_scores table:"
      );
      console.log(`
ALTER TABLE brand_scores ADD COLUMN IF NOT EXISTS score_data JSONB;
ALTER TABLE brand_scores ADD COLUMN IF NOT EXISTS labor_practices_summary TEXT;
ALTER TABLE brand_scores ADD COLUMN IF NOT EXISTS labor_practices_rating TEXT;
ALTER TABLE brand_scores ADD COLUMN IF NOT EXISTS sustainability_summary TEXT;
ALTER TABLE brand_scores ADD COLUMN IF NOT EXISTS sustainability_rating TEXT;
ALTER TABLE brand_scores ADD COLUMN IF NOT EXISTS ethical_sourcing_summary TEXT;
ALTER TABLE brand_scores ADD COLUMN IF NOT EXISTS ethical_sourcing_rating TEXT;
ALTER TABLE brand_scores ADD COLUMN IF NOT EXISTS donations_summary TEXT;
ALTER TABLE brand_scores ADD COLUMN IF NOT EXISTS donations_rating TEXT;
ALTER TABLE brand_scores ADD COLUMN IF NOT EXISTS overall_sentiment_summary TEXT;
ALTER TABLE brand_scores ADD COLUMN IF NOT EXISTS overall_rating TEXT;
ALTER TABLE brand_scores ADD COLUMN IF NOT EXISTS sources JSONB;
      `);
    } catch (error) {
      console.error("GoodGoods: Error in save attempts:", error);
    }
  } catch (error) {
    console.error(
      "GoodGoods: Error saving to cache:",
      error instanceof Error ? error.message : JSON.stringify(error)
    );
    // Don't throw the error, just log it - this allows the UI to continue working even if caching fails
  }
}

// Mock implementation for alternative products cache
async function mockAlternativesCache(
  category: string,
  currentBrand: string,
  currentRating: OverallRating
): Promise<AlternativeProduct[] | null> {
  console.log("GoodGoods: Using mock alternatives cache for", category, "from", currentBrand);

  // Simulate a cache miss for testing the Perplexity API flow
  return null;
}

/**
 * Get alternative products from cache
 * @param category Product category
 * @param currentBrand Current brand
 * @param currentRating Current brand's rating
 * @returns Array of alternative products or null if not in cache
 */
export async function getAlternativesFromCache(
  category: string,
  currentBrand: string,
  currentRating: OverallRating
): Promise<AlternativeProduct[] | null> {
  try {
    // Try to get the Supabase client
    const supabase = await getSupabaseClient();

    // If we don't have valid credentials, use mock implementation
    if (!supabase) {
      console.log("GoodGoods: No Supabase credentials, using mock alternatives cache");
      return mockAlternativesCache(category, currentBrand, currentRating);
    }

    console.log("GoodGoods: Querying alternatives cache for", category, "from", currentBrand);

    // Create a cache key that uniquely identifies this request
    const cacheKey = `${currentBrand.toLowerCase()}_${category.toLowerCase()}_${currentRating}`;

    const { data, error } = await supabase
      .from("alternative_products")
      .select("*")
      .eq("cache_key", cacheKey)
      .maybeSingle();

    if (error) {
      console.error("GoodGoods: Supabase error when fetching alternatives:", JSON.stringify(error));
      return null;
    }

    if (!data) {
      console.log("GoodGoods: No alternatives found in cache");
      return null;
    }

    // Check if cache is expired (7 days)
    const lastUpdated = new Date(data.last_updated);
    const now = new Date();
    const daysSinceUpdate = (now.getTime() - lastUpdated.getTime()) / (1000 * 60 * 60 * 24);

    if (daysSinceUpdate > 7) {
      console.log("GoodGoods: Alternatives cache expired");
      return null;
    }

    console.log("GoodGoods: Found alternatives in cache");

    // Parse the alternatives from the JSON string
    try {
      const alternatives = JSON.parse(data.alternatives_data);
      return alternatives as AlternativeProduct[];
    } catch (parseError) {
      console.error("GoodGoods: Error parsing alternatives data:", parseError);
      return null;
    }
  } catch (error) {
    console.error(
      "GoodGoods: Error accessing alternatives cache:",
      error instanceof Error ? error.message : JSON.stringify(error)
    );
    return null;
  }
}

/**
 * Save alternative products to cache
 * @param category Product category
 * @param currentBrand Current brand
 * @param currentRating Current brand's rating
 * @param alternatives Array of alternative products
 */
export async function saveAlternativesToCache(
  category: string,
  currentBrand: string,
  currentRating: OverallRating,
  alternatives: AlternativeProduct[]
): Promise<void> {
  try {
    // Try to get the Supabase client
    const supabase = await getSupabaseClient();

    // If we don't have valid credentials, just log and return
    if (!supabase) {
      console.log("GoodGoods: No Supabase credentials, skipping alternatives cache save");
      return;
    }

    console.log("GoodGoods: Saving alternatives to cache for", category, "from", currentBrand);

    // Create a cache key that uniquely identifies this request
    const cacheKey = `${currentBrand.toLowerCase()}_${category.toLowerCase()}_${currentRating}`;

    // Prepare the data for saving
    const cacheData = {
      cache_key: cacheKey,
      category: category,
      current_brand: currentBrand,
      current_rating: currentRating,
      alternatives_data: JSON.stringify(alternatives),
      last_updated: new Date().toISOString(),
    };

    // Try to save to the database
    const { error } = await supabase.from("alternative_products").upsert([cacheData]);

    if (error) {
      console.error("GoodGoods: Error saving alternatives to cache:", JSON.stringify(error));

      // If the table doesn't exist, log a more helpful message
      if (error.code === "42P01") {
        console.error(
          "GoodGoods: The alternative_products table does not exist. Please create it with the following SQL:"
        );
        console.error(`
CREATE TABLE alternative_products (
  id uuid primary key DEFAULT gen_random_uuid(),
  cache_key text NOT NULL,
  category text NOT NULL,
  current_brand text NOT NULL,
  current_rating text NOT NULL,
  alternatives_data jsonb NOT NULL,
  last_updated timestamp with time zone DEFAULT now()
);

CREATE UNIQUE INDEX idx_alternative_products_cache_key ON alternative_products(cache_key);
CREATE INDEX idx_alternative_products_category ON alternative_products(category);
CREATE INDEX idx_alternative_products_current_brand ON alternative_products(current_brand);
CREATE INDEX idx_alternative_products_last_updated ON alternative_products(last_updated);
        `);
      }
      return;
    }

    console.log("GoodGoods: Successfully saved alternatives to cache");
  } catch (error) {
    console.error(
      "GoodGoods: Error saving alternatives to cache:",
      error instanceof Error ? error.message : JSON.stringify(error)
    );
    // Don't throw the error, just log it - this allows the UI to continue working even if caching fails
  }
}
