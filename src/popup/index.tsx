import React, { useState, useEffect } from "react";
import { createRoot } from "react-dom/client";
import { AuthProvider, useAuth } from "../contexts/AuthContext";
import AuthForm from "../components/Auth/AuthForm";
import "./popup.css";

const Popup: React.FC = () => {
  return (
    <AuthProvider>
      <PopupContent />
    </AuthProvider>
  );
};

const PopupContent: React.FC = () => {
  const { isAnonymous, currentUser } = useAuth();
  const [currentUrl, setCurrentUrl] = useState<string>("");
  const [isAmazonProduct, setIsAmazonProduct] = useState<boolean>(false);

  useEffect(() => {
    // Get the current tab URL
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      const url = tabs[0]?.url || "";
      setCurrentUrl(url);

      // Check if it's an Amazon product page
      setIsAmazonProduct(url.includes("amazon.com") && url.includes("/dp/"));
    });
  }, []);

  const visitAmazon = () => {
    chrome.tabs.create({ url: "https://www.amazon.com" });
  };

  const viewCurrentProduct = () => {
    // This will refresh the current tab, which will trigger the content script
    chrome.tabs.reload();
    window.close(); // Close the popup
  };
  console.log("something here");
  console.log(isAnonymous);

  if (isAnonymous) {
    return (
      <div className="popup-container">
        <div className="popup-header">
          <h1 className="popup-title">GoodGoods</h1>
        </div>
        <AuthForm className="mt-4" />
      </div>
    );
  }

  return (
    <div className="popup-container">
      <div className="popup-header">
        <h1 className="popup-title">ButtNuggets</h1>
      </div>

      <p className="popup-description">
        Welcome back{currentUser?.displayName ? `, ${currentUser.displayName}` : ""}! Browse Amazon
        products with confidence. GoodGoods shows you ethical scores for brands based on:
      </p>

      <ul className="feature-list">
        <li>Labor Practices</li>
        <li>Sustainability</li>
        <li>Ethical Sourcing</li>
        <li>Charitable Donations</li>
        <li>Overall Impact</li>
      </ul>

      <div className="button-container">
        {isAmazonProduct ? (
          <button className="primary-button" onClick={viewCurrentProduct}>
            View Ethical Score
          </button>
        ) : (
          <button className="primary-button" onClick={visitAmazon}>
            Go to Amazon
          </button>
        )}
      </div>

      <div className="features-section">
        <h2 className="section-title">New Features</h2>
        <ul className="feature-list">
          <li>Expandable score details</li>
          <li>Alternative product suggestions directly in the badge</li>
          <li>Improved user experience</li>
        </ul>
      </div>

      <p className="footer-text">Visit any Amazon product page to see the ethical score badge.</p>
    </div>
  );
};

const container = document.getElementById("root");
if (container) {
  const root = createRoot(container);
  root.render(
    <React.StrictMode>
      <Popup />
    </React.StrictMode>
  );
}
