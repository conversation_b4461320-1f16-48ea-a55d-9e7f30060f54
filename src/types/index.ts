export type Rating = "green" | "yellow" | "red";
export type OverallRating = "A" | "B" | "C" | "D";

export interface DimensionScore {
  summary: string;
  rating: Rating;
}

export interface EthicalScore {
  labor_practices: DimensionScore;
  sustainability: DimensionScore;
  ethical_sourcing: DimensionScore;
  donations: DimensionScore;
  overall_sentiment: {
    summary: string;
  };
  overall_rating: OverallRating;
  sources: string[];
}

export interface CachedScore extends EthicalScore {
  brand: string;
  last_updated: string;
}

export interface BrandDetectionResult {
  brand: string;
  confidence: number;
}

export interface AlternativeProduct {
  name: string;
  brand: string;
  description: string;
  price?: string;
  url?: string;
  search_url?: string;
  image_url?: string;
  overall_rating: OverallRating;
  reason: string;
}

export interface CachedAlternatives {
  category: string;
  current_brand: string;
  current_rating: OverallRating;
  alternatives: AlternativeProduct[];
  last_updated: string;
}
