// Type definitions for Chrome API
declare namespace chrome {
  namespace storage {
    interface StorageArea {
      get(keys: string | string[] | null | Record<string, any>): Promise<Record<string, any>>;
      set(items: Record<string, any>): Promise<void>;
      remove(keys: string | string[]): Promise<void>;
      clear(): Promise<void>;
    }

    const local: StorageArea;
    const sync: StorageArea;
  }
}
