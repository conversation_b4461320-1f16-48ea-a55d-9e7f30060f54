/* Base styles */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f9fafb;
}

/* Container */
.alternatives-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.alternatives-header {
  background-color: white;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alternatives-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2563eb;
  margin: 0;
}

.back-button {
  background-color: #e5e7eb;
  color: #374151;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
}

.back-button:hover {
  background-color: #d1d5db;
}

/* Main content */
.alternatives-content {
  flex: 1;
  background-color: white;
  border-radius: 8px;
  margin: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Footer */
.alternatives-footer {
  padding: 16px;
  text-align: center;
  color: #6b7280;
  font-size: 0.75rem;
  border-top: 1px solid #e5e7eb;
}

/* Utility classes for AlternativeProducts component */
.p-4 {
  padding: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-xs {
  font-size: 0.75rem;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.text-red-500 {
  color: #ef4444;
}

.text-gray-600 {
  color: #4b5563;
}

.text-green-700 {
  color: #047857;
}

.text-green-800 {
  color: #065f46;
}

.text-blue-600 {
  color: #2563eb;
}

.bg-green-100 {
  background-color: #d1fae5;
}

.border {
  border: 1px solid #e5e7eb;
}

.rounded {
  border-radius: 0.25rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.p-3 {
  padding: 0.75rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-start {
  align-items: flex-start;
}

.hover\:shadow-md:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.hover\:underline:hover {
  text-decoration: underline;
}
