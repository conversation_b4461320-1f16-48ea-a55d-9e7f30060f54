import React, { useEffect, useState } from "react";
import { createRoot } from "react-dom/client";
import { AlternativeProducts } from "../components/AlternativeProducts";
import { getAlternativeProducts } from "../utils/openai";
import type { AlternativeProduct, OverallRating } from "../types";
import "./alternatives.css";

const AlternativesPage: React.FC = () => {
  const [products, setProducts] = useState<AlternativeProduct[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [category, setCategory] = useState<string>("");
  const [brand, setBrand] = useState<string>("");
  const [rating, setRating] = useState<OverallRating>("B");

  useEffect(() => {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const categoryParam = urlParams.get("category") || "product";
    const brandParam = urlParams.get("brand") || "Unknown";
    const ratingParam = (urlParams.get("rating") as OverallRating) || "B";

    console.log("GoodGoods: Alternatives page loaded with parameters:", {
      category: categoryParam,
      brand: brandParam,
      rating: ratingParam,
    });

    setCategory(categoryParam);
    setBrand(brandParam);
    setRating(ratingParam);

    const fetchAlternatives = async () => {
      try {
        setIsLoading(true);
        console.log("GoodGoods: Fetching alternative products...");
        const alternatives = await getAlternativeProducts(categoryParam, brandParam, ratingParam);
        console.log("GoodGoods: Received alternatives:", alternatives);
        setProducts(alternatives);
        setIsLoading(false);
      } catch (err) {
        console.error("GoodGoods: Error fetching alternatives:", err);
        setError("Failed to fetch alternative products. Please try again later.");
        setIsLoading(false);
      }
    };

    fetchAlternatives();
  }, []);

  const goBack = () => {
    // Try to get the referrer URL
    const referrer = document.referrer;
    if (referrer && referrer.includes("amazon.com")) {
      // If we have a valid Amazon referrer, navigate back to it
      window.location.href = referrer;
    } else {
      // Otherwise just close the tab
      window.close();
    }
  };

  return (
    <div className="alternatives-container">
      <header className="alternatives-header">
        <h1 className="alternatives-title">GoodGoods</h1>
        <button className="back-button" onClick={goBack}>
          &larr; Back
        </button>
      </header>

      <main className="alternatives-content">
        <AlternativeProducts
          products={products}
          category={category}
          currentBrand={brand}
          isLoading={isLoading}
          error={error || undefined}
        />
      </main>

      <footer className="alternatives-footer">
        <p>
          GoodGoods helps you find more ethical and sustainable alternatives to your favorite
          products.
        </p>
      </footer>
    </div>
  );
};

const container = document.getElementById("root");
if (container) {
  const root = createRoot(container);
  root.render(
    <React.StrictMode>
      <AlternativesPage />
    </React.StrictMode>
  );
}
