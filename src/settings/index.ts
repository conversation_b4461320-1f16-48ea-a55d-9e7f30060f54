document.addEventListener("DOMContentLoaded", () => {
  const openaiApiKeyInput = document.getElementById("openaiApiKey") as HTMLInputElement;
  const supabaseUrlInput = document.getElementById("supabaseUrl") as HTMLInputElement;
  const supabaseKeyInput = document.getElementById("supabaseKey") as HTMLInputElement;
  const saveButton = document.getElementById("saveButton") as HTMLButtonElement;
  const successMessage = document.getElementById("successMessage") as HTMLParagraphElement;
  const errorMessage = document.getElementById("errorMessage") as HTMLParagraphElement;

  // Load saved settings
  chrome.storage.local.get(["openaiApiKey", "supabaseUrl", "supabaseKey"], (result) => {
    if (result.openaiApiKey) {
      openaiApiKeyInput.value = result.openaiApiKey;
    }
    if (result.supabaseUrl) {
      supabaseUrlInput.value = result.supabaseUrl;
    }
    if (result.supabaseKey) {
      supabaseKeyInput.value = result.supabaseKey;
    }
  });

  // Save settings
  saveButton.addEventListener("click", () => {
    const openaiApiKey = openaiApiKeyInput.value.trim();
    const supabaseUrl = supabaseUrlInput.value.trim();
    const supabaseKey = supabaseKeyInput.value.trim();

    // Basic validation
    if (!openaiApiKey && !supabaseUrl && !supabaseKey) {
      showError("Please enter at least one API key");
      return;
    }

    // Save to Chrome storage
    chrome.storage.local.set(
      {
        openaiApiKey,
        supabaseUrl,
        supabaseKey,
      },
      () => {
        if (chrome.runtime.lastError) {
          showError("Error saving settings: " + chrome.runtime.lastError.message);
        } else {
          showSuccess();
        }
      }
    );
  });

  function showSuccess() {
    successMessage.style.display = "block";
    errorMessage.style.display = "none";

    // Hide success message after 3 seconds
    setTimeout(() => {
      successMessage.style.display = "none";
    }, 3000);
  }

  function showError(message: string) {
    errorMessage.textContent = message;
    errorMessage.style.display = "block";
    successMessage.style.display = "none";

    // Hide error message after 5 seconds
    setTimeout(() => {
      errorMessage.style.display = "none";
    }, 5000);
  }
});
