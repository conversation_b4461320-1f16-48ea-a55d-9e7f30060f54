<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GoodGoods Settings</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      padding: 20px;
      max-width: 600px;
      margin: 0 auto;
    }
    h1 {
      color: #2563eb;
      margin-bottom: 20px;
    }
    .form-group {
      margin-bottom: 20px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
    }
    input[type="text"] {
      width: 100%;
      padding: 8px;
      border: 1px solid #d1d5db;
      border-radius: 4px;
      font-size: 14px;
    }
    .api-key-input {
      font-family: monospace;
    }
    button {
      background-color: #2563eb;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    button:hover {
      background-color: #1d4ed8;
    }
    .success-message {
      color: #10b981;
      margin-top: 10px;
      display: none;
    }
    .error-message {
      color: #ef4444;
      margin-top: 10px;
      display: none;
    }
    .info-text {
      font-size: 14px;
      color: #6b7280;
      margin-top: 5px;
    }
  </style>
</head>
<body>
  <div id="root">
    <h1>GoodGoods Settings</h1>
    
    <div class="form-group">
      <label for="openaiApiKey">OpenAI API Key</label>
      <input type="text" id="openaiApiKey" class="api-key-input" placeholder="sk-...">
      <p class="info-text">
        Get your API key from <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI API Keys</a>
      </p>
    </div>
    
    <div class="form-group">
      <label for="supabaseUrl">Supabase URL</label>
      <input type="text" id="supabaseUrl" placeholder="https://xxxxxxxxxxxxxxxxxxxx.supabase.co">
    </div>
    
    <div class="form-group">
      <label for="supabaseKey">Supabase Anon Key</label>
      <input type="text" id="supabaseKey" class="api-key-input" placeholder="eyJxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx">
    </div>
    
    <button id="saveButton">Save Settings</button>
    
    <p id="successMessage" class="success-message">Settings saved successfully!</p>
    <p id="errorMessage" class="error-message">Error saving settings. Please try again.</p>
  </div>
  
  <script src="index.js"></script>
</body>
</html>
