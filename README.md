# GoodGoods Chrome Extension

A Chrome extension that displays ethical scores for products on Amazon. The extension shows a simple badge with detailed information about a brand's labor practices, sustainability, ethical sourcing, charitable donations, and overall impact.

## Features

- Automatically detects brand names on Amazon product pages
- Displays a color-coded badge with ethical scores
- Shows detailed breakdowns of different ethical dimensions
- Caches results for 30 days for better performance
- Uses OpenAI GPT-4 for up-to-date ethical evaluations

## Setup

### Prerequisites

- Node.js 18 or later
- Firebase CLI (`npm install -g firebase-tools`)
- Firebase project (create at [Firebase Console](https://console.firebase.google.com/))
- OpenAI API key

### Backend Setup (Firebase Functions)

1. Navigate to the functions directory and install dependencies:

   ```bash
   cd functions
   npm install
   ```

2. Set up environment variables:

   ```bash
   cp .env.example .env
   # Edit .env and add your OpenAI API key
   ```

3. Deploy the Firebase functions:

   ```bash
   firebase deploy --only functions
   ```

   Note the function URLs that are output after deployment.

### Chrome Extension Setup

1. Clone the repository (if you haven't already):

   ```bash
   git clone https://github.com/yourusername/social-goods.git
   cd social-goods
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Create a `.env` file in the root directory with your configuration:

   ```
   # Firebase configuration (from your Firebase project settings)
   NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

   # Supabase configuration (if using)
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_anon_key

   # Chrome extension directory (optional)
   CHROME_EXTENSION_DIR=/path/to/chrome/extension/directory
   ```

   The `CHROME_EXTENSION_DIR` should point to a directory where you want the extension to be copied during development. This is typically a directory within Chrome's extension folder. If not specified, the build script will try to detect it automatically.

4. Build the extension:

```bash
npm run build
```

5. Load the extension in Chrome:
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode" in the top right
   - Click "Load unpacked" and select the `dist` directory

## Development

### Build Options

The project includes a custom build script with several options:

```bash
# Production build (will try to copy to Chrome extension directory)
npm run build

# Development build with file watching (will try to copy to Chrome extension directory)
npm run dev

# Development build with file watching (no copy, use dist/ folder)
npm run dev:local

# Production build (no copy, use dist/ folder)
npm run build:local
```

## Database Schema

The application is designed to be flexible with the database schema. It will adapt to whatever schema is available, storing the data in the most appropriate format. The current implementation expects a table with at least the `brand` and `score` columns, where `score` is a numeric (double precision) type. The application converts between letter grades (A, B, C, D) and numeric scores (4.0, 3.0, 2.0, 1.0) automatically.

For optimal functionality, the table should include the `score_data` JSONB column to store the complete data returned from the OpenAI API. This ensures that all ethical score details are preserved in the cache.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

MIT
