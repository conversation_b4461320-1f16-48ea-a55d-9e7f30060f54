

# **Product Requirements Document (PRD)**  
**Product Name:** GoodGoods Chrome Extension (MVP)  
**Prepared For:** Cursor Task Creator Agents  
**Prepared By:** <PERSON> (Builder), with co-founder  

---

## **1. Overview & Purpose**

GoodGoods is a Chrome extension MVP for eco-conscious shoppers, starting with Amazon. It overlays a simple ethical "Social Goodness Score" on product pages, derived from aggregated online data. This MVP aims to test market viability with minimal scope and effort by automating data retrieval and UI integration.

---

## **2. Objective**

Build a functional MVP Chrome Extension that:

- Detects brand/company from an Amazon product page  
- Requests ethical evaluations from the Perplexity API  
- Displays a summary score and per-dimension details via an intuitive UI  
- Caches results for performance and cost-efficiency  

This MVP is intended for early validation with minimal human involvement.

---

## **3. Target Users**

- **Primary**: Millennials and Gen Z consumers who value ethics and sustainability  
- **Secondary**: Conscious consumers using Amazon for convenience  
- **User Need**: A no-effort overlay that informs and potentially shifts buying choices  

---

## **4. Core Features**

### 4.1 Website Support  
- **Amazon.com** only (product detail pages)  

### 4.2 Brand Detection  
- Parse the **brand/manufacturer/company** name from Amazon product pages  
- Acceptable fallback: hardcoded logic or regex heuristics (e.g., extract from `By [Brand]`)  

### 4.3 Social Goodness Score  
- Display a **color-coded badge** (green/yellow/red) fixed in a non-obtrusive location  
- Badge expands on **hover or click**, revealing:  
  - Each category rating (color-coded, labeled)  
  - One-sentence summary per category  
  - Expandable details inline under each category  
  - Link to original source or citation (when available)  

### 4.4 Ethical Dimensions Evaluated  
Each company score includes:  

1. Labor Practices  
2. Sustainability  
3. Ethical Sourcing  
4. Charitable Donations / Community Impact  
5. Overall Sentiment  
6. Overall Letter Grade (A–D)

### 4.5 Data Source — Perplexity API  

For each new brand:
- Request structured ethical data using a prompt like:

  > “Please evaluate [COMPANY NAME] across Labor Practices, Sustainability, Ethical Sourcing, Charitable Donations, and Overall Sentiment. Provide 1-sentence summaries and color scores (green/yellow/red) per dimension. Give an Overall Grade (A/B/C/D). Respond in this JSON format: ..."

Expected JSON structure:

```json
{
  "labor_practices": {"summary": "string", "rating": "green"},
  "sustainability": {"summary": "string", "rating": "yellow"},
  "ethical_sourcing": {"summary": "string", "rating": "red"},
  "donations": {"summary": "string", "rating": "green"},
  "overall_sentiment": {"summary": "string"},
  "overall_rating": "B",
  "sources": ["https://..."]
}
```

---

## **5. Technical Architecture**

### 5.1 Chrome Extension  
- Injects UI on **Amazon product pages** only  
- Executes content script to extract brand info  
- Queries backend with detected brand  
- Receives structured JSON response  
- Renders UI badge + expandable ethical breakdown  

### 5.2 Backend Server  
- Receives brand name queries  
- Checks if brand data is in cache  
- If not cached: calls Perplexity API, stores response  
- Caches results for **30 days** using timestamp  

### 5.3 Database (Supabase / PostgreSQL)  
Schema Example:

```json
{
  "brand": "string",
  "labor_practices": {"summary": "string", "rating": "string"},
  "sustainability": {"summary": "string", "rating": "string"},
  "ethical_sourcing": {"summary": "string", "rating": "string"},
  "donations": {"summary": "string", "rating": "string"},
  "overall_sentiment": {"summary": "string"},
  "overall_rating": "string",
  "sources": ["string"],
  "last_updated": "datetime"
}
```

---

## **6. Design Requirements**

- UI takes visual cues from Honey (compact, non-intrusive)  
- Badge uses **traffic light colors** (green/yellow/red)  
- Hover/click opens detail view inline  
- One-line summaries improve scanning  
- Font and iconography minimal and readable  
- Include expandable source links where applicable  

---

## **7. Non-Goals / Deferred Scope**

- Non-Amazon sites  
- Suggesting alternative ethical products (future feature)  
- Advanced brand parsing via ML  
- User login, personalization, or bookmarks  

---

## **8. MVP Completion Criteria**

- Chrome extension injects badge on Amazon product pages  
- Brand name successfully parsed or hardcoded for MVP  
- Badge shows accurate score from backend  
- JSON response stored in DB with 30-day cache  
- Perplexity responses structured and useful  
- At least 50 brands tested  
- Positive user feedback on clarity, usefulness  

---

## **9. Agent Instructions & Notes**

- Code agent will need to:  
  - Build a minimal UI component (badge + expansion)  
  - Set up Supabase backend with cache + endpoint  
  - Call Perplexity API using structured prompt  
  - Parse and store data as per schema  
  - Ensure all frontend–backend wiring is complete

- Task agent:  
  - Break PRD into atomic, serializable steps for coding agent  
  - Clearly define tasks like brand parsing, cache checking, etc.  

---

**End of Document**  

Let me know if you'd like a version broken into GitHub issues or task bullet points.