{"functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"], "runtime": "nodejs18", "region": "us-central1", "timeoutSeconds": 60, "memory": "256MB"}], "hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/api/score", "function": "getEthicalScore"}, {"source": "/api/alternatives", "function": "getAlternativeProducts"}, {"source": "/api/health", "function": "healthCheck"}, {"source": "**", "destination": "/index.html"}]}}