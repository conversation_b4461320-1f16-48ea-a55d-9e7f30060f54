# Required
ANTHROPIC_API_KEY=your-api-key-here  # For most AI ops -- Format: sk-ant-api03-... (Required)
PERPLEXITY_API_KEY=pplx-abcde        # For research -- Format: pplx-abcde (Optional, Highly Recommended)
SUPABASE_URL=your_supabase_url       # Supabase URL for database access
SUPABASE_ANON_KEY=your_supabase_key  # Supabase anonymous key for database access

# Chrome Extension Development
CHROME_EXTENSION_DIR=/path/to/chrome/extension/directory  # Directory where Chrome extensions are stored

# Optional - defaults shown
MODEL=claude-3-7-sonnet-20250219  # Recommended models: claude-3-7-sonnet-20250219, claude-3-opus-20240229 (Required)
PERPLEXITY_MODEL=sonar-pro        # Make sure you have access to sonar-pro otherwise you can use sonar regular (Optional)
MAX_TOKENS=64000                   # Maximum tokens for model responses (Required)
TEMPERATURE=0.2                   # Temperature for model responses (0.0-1.0) - lower = less creativity and follow your prompt closely (Required)
DEBUG=false                       # Enable debug logging (true/false)
LOG_LEVEL=info                    # Log level (debug, info, warn, error)
DEFAULT_SUBTASKS=5                # Default number of subtasks when expanding
DEFAULT_PRIORITY=medium           # Default priority for generated tasks (high, medium, low)
PROJECT_NAME={{projectName}}      # Project name for tasks.json metadata