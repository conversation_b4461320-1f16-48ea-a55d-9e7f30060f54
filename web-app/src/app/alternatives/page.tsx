"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { AlternativesTokenPayload } from "@/lib/jwt";

interface Alternative {
  name: string;
  rating: string;
  description: string;
  searchUrl: string;
  price?: string;
  image?: string;
}

export default function AlternativesPage() {
  const searchParams = useSearchParams();
  const [tokenData, setTokenData] = useState<AlternativesTokenPayload | null>(null);
  const [alternatives, setAlternatives] = useState<Alternative[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const token = searchParams.get("token");

    if (!token) {
      setError("No token provided");
      setLoading(false);
      return;
    }

    validateTokenAndFetchAlternatives(token);
  }, [searchParams]);

  const validateTokenAndFetchAlternatives = async (token: string) => {
    try {
      // Validate token
      const response = await fetch("/api/validate-token", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token }),
      });

      if (!response.ok) {
        throw new Error("Invalid token");
      }

      const { payload } = await response.json();
      setTokenData(payload);

      // Fetch alternatives (mock data for now)
      const mockAlternatives: Alternative[] = [
        {
          name: "Patagonia",
          rating: "A",
          description: "Sustainable outdoor clothing with excellent environmental practices",
          searchUrl: `https://www.google.com/search?q=Patagonia+${payload.category}+products`,
          price: "$45-120",
        },
        {
          name: "Eileen Fisher",
          rating: "A",
          description: "Ethical fashion brand committed to sustainability",
          searchUrl: `https://www.google.com/search?q=Eileen+Fisher+${payload.category}`,
          price: "$80-200",
        },
        {
          name: "Everlane",
          rating: "B+",
          description: "Transparent pricing and ethical manufacturing",
          searchUrl: `https://www.google.com/search?q=Everlane+${payload.category}+shop`,
          price: "$30-150",
        },
      ];

      setAlternatives(mockAlternatives);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load alternatives");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading alternatives...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Better Alternatives to {tokenData?.brand}
              </h1>
              <p className="mt-2 text-gray-600">
                Current rating:{" "}
                <span className="font-semibold text-red-600">{tokenData?.currentRating}</span> •
                Category: <span className="font-semibold">{tokenData?.category}</span>
              </p>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">Powered by</div>
              <div className="text-lg font-bold text-primary-600">Social Goods</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Info Banner */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
          <div className="flex items-start">
            <div className="text-blue-500 text-xl mr-3">💡</div>
            <div>
              <h3 className="text-blue-900 font-semibold">Why consider alternatives?</h3>
              <p className="text-blue-800 mt-1">
                The brand you're viewing has a <strong>{tokenData?.currentRating}</strong> rating
                for social and environmental responsibility. Here are some better-rated alternatives
                in the same category.
              </p>
            </div>
          </div>
        </div>

        {/* Alternatives Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {alternatives.map((alternative, index) => (
            <div
              key={index}
              className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-gray-900">{alternative.name}</h3>
                  <span
                    className={`px-3 py-1 rounded-full text-sm font-semibold ${
                      alternative.rating === "A"
                        ? "bg-green-100 text-green-800"
                        : "bg-blue-100 text-blue-800"
                    }`}
                  >
                    Rating: {alternative.rating}
                  </span>
                </div>

                <p className="text-gray-600 mb-4">{alternative.description}</p>

                {alternative.price && (
                  <p className="text-sm text-gray-500 mb-4">Price range: {alternative.price}</p>
                )}

                <a
                  href={alternative.searchUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center w-full px-4 py-2 bg-primary-600 text-white font-semibold rounded-lg hover:bg-primary-700 transition-colors duration-200"
                >
                  Shop {alternative.name}
                  <svg
                    className="ml-2 w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                    />
                  </svg>
                </a>
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className="mt-12 text-center">
          <p className="text-gray-500 text-sm">
            Want to learn more about ethical shopping?
            <a href="#" className="text-primary-600 hover:text-primary-700 ml-1">
              Visit our website
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
