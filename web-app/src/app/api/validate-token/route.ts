import { NextRequest, NextResponse } from "next/server";
import { verifyAlternativesToken } from "@/lib/jwt";

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json({ error: "Token is required" }, { status: 400 });
    }

    const payload = verifyAlternativesToken(token);

    if (!payload) {
      return NextResponse.json({ error: "Invalid or expired token" }, { status: 401 });
    }

    return NextResponse.json({
      valid: true,
      payload: {
        brand: payload.brand,
        category: payload.category,
        currentRating: payload.currentRating,
        timestamp: payload.timestamp,
      },
    });
  } catch (error) {
    console.error("Token validation error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}
