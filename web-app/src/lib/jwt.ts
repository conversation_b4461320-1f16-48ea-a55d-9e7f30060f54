import jwt from "jsonwebtoken";

// This should be the same secret used in the Chrome extension
const JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-key-change-in-production";

export interface AlternativesTokenPayload {
  brand: string;
  category: string;
  currentRating: string;
  timestamp: number;
  exp: number; // Expiration time
}

/**
 * Verify and decode a JWT token
 * @param token - The JWT token to verify
 * @returns Decoded payload or null if invalid
 */
export function verifyAlternativesToken(token: string): AlternativesTokenPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as AlternativesTokenPayload;

    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    if (decoded.exp && decoded.exp < now) {
      return null;
    }

    return decoded;
  } catch (error) {
    console.error("JWT verification failed:", error);
    return null;
  }
}

/**
 * Generate a JWT token (for testing purposes)
 * @param brand - The current brand name
 * @param category - The product category
 * @param currentRating - The current brand's rating (A, B, C, D)
 * @returns Signed JWT token
 */
export function generateAlternativesToken(
  brand: string,
  category: string,
  currentRating: string
): string {
  const now = Math.floor(Date.now() / 1000);
  const payload: AlternativesTokenPayload = {
    brand,
    category,
    currentRating,
    timestamp: now,
    exp: now + 60 * 15, // Token expires in 15 minutes
  };

  return jwt.sign(payload, JWT_SECRET);
}
