const path = require("path");
const CopyPlugin = require("copy-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const webpack = require("webpack");
const dotenv = require("dotenv");

// Load environment variables from .env file
const dotenvResult = dotenv.config();
const env = {
  // Default values (will be overridden by .env if available)
  OPENAI_API_KEY: "",
  SUPABASE_URL: "",
  SUPABASE_ANON_KEY: "",
};

// If .env file was loaded successfully, use those values
if (dotenvResult.parsed) {
  Object.assign(env, dotenvResult.parsed);
}

// For production, you can set these via environment variables in your CI/CD pipeline
if (process.env.NODE_ENV === "production") {
  if (process.env.OPENAI_API_KEY) env.OPENAI_API_KEY = process.env.OPENAI_API_KEY;
  if (process.env.NEXT_PUBLIC_SUPABASE_URL) env.SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
  else if (process.env.SUPABASE_URL) env.SUPABASE_URL = process.env.SUPABASE_URL;
  if (process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)
    env.SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  else if (process.env.SUPABASE_ANON_KEY) env.SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY;
}

module.exports = {
  mode: "development",
  devtool: "source-map",
  entry: {
    popup: "./src/popup/index.tsx",
    content: "./src/content/index.tsx",
    background: "./src/background/index.ts",
    alternatives: "./src/alternatives/index.tsx",
  },
  output: {
    path: path.resolve(__dirname, "dist"),
    filename: "[name].js",
    publicPath: "/",
  },
  // Add devServer configuration
  devServer: {
    static: {
      directory: path.join(__dirname, "dist"),
    },
    allowedHosts: "all",
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    port: 3000,
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: "ts-loader",
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        use: ["style-loader", "css-loader"],
      },
    ],
  },
  resolve: {
    extensions: [".tsx", ".ts", ".js"],
  },
  plugins: [
    new CopyPlugin({
      patterns: [{ from: "public" }],
    }),
    new HtmlWebpackPlugin({
      template: "./src/popup/index.html",
      filename: "popup.html",
      chunks: ["popup"],
    }),
    new HtmlWebpackPlugin({
      template: "./src/alternatives/index.html",
      filename: "alternatives.html",
      chunks: ["alternatives"],
    }),
    new webpack.DefinePlugin({
      "process.env.OPENAI_API_KEY": JSON.stringify(env.OPENAI_API_KEY),
      "process.env.SUPABASE_URL": JSON.stringify(env.SUPABASE_URL),
      "process.env.SUPABASE_ANON_KEY": JSON.stringify(env.SUPABASE_ANON_KEY),
      "process.env.NEXT_PUBLIC_SUPABASE_URL": JSON.stringify(env.SUPABASE_URL),
      "process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY": JSON.stringify(env.SUPABASE_ANON_KEY),
    }),
  ],
};
