-- Create a brand_scores table that matches the actual schema
DROP TABLE IF EXISTS brand_scores;

CREATE TABLE brand_scores (
  -- Primary key
  id uuid primary key DEFAULT gen_random_uuid(),

  -- Brand name
  brand text NOT NULL,

  -- Score (required field) - numeric type
  score double precision NOT NULL,

  -- Timestamp
  last_updated timestamp with time zone DEFAULT now(),

  -- Complete score data as JSON
  score_data jsonb,

  -- Individual fields for easier querying
  labor_practices_summary text,
  labor_practices_rating text,
  sustainability_summary text,
  sustainability_rating text,
  ethical_sourcing_summary text,
  ethical_sourcing_rating text,
  donations_summary text,
  donations_rating text,
  overall_sentiment_summary text,
  overall_rating text,
  sources jsonb
);

-- Add indexes for better performance
CREATE INDEX idx_brand_scores_brand ON brand_scores(brand);
CREATE INDEX idx_brand_scores_last_updated ON brand_scores(last_updated);
CREATE INDEX idx_brand_scores_score ON brand_scores(score);

-- Add a unique constraint on brand to ensure we don't have duplicates
CREATE UNIQUE INDEX idx_brand_scores_brand_unique ON brand_scores(brand);

-- Add a comment to the table
COMMENT ON TABLE brand_scores IS 'Cached ethical scores for brands from Perplexity API';

-- Create a table for caching alternative products
DROP TABLE IF EXISTS alternative_products;

CREATE TABLE alternative_products (
  -- Primary key
  id uuid primary key DEFAULT gen_random_uuid(),

  -- Cache key (combination of brand, category, and rating)
  cache_key text NOT NULL,

  -- Product category
  category text NOT NULL,

  -- Current brand
  current_brand text NOT NULL,

  -- Current rating
  current_rating text NOT NULL,

  -- Alternative products data as JSON
  alternatives_data jsonb NOT NULL,

  -- Timestamp
  last_updated timestamp with time zone DEFAULT now()
);

-- Add indexes for better performance
CREATE UNIQUE INDEX idx_alternative_products_cache_key ON alternative_products(cache_key);
CREATE INDEX idx_alternative_products_category ON alternative_products(category);
CREATE INDEX idx_alternative_products_current_brand ON alternative_products(current_brand);
CREATE INDEX idx_alternative_products_last_updated ON alternative_products(last_updated);

-- Add a comment to the table
COMMENT ON TABLE alternative_products IS 'Cached alternative products from Perplexity API';
