-- Create the alternative_products table for alternative product recommendations
CREATE TABLE IF NOT EXISTS public.alternative_products (
  id SERIAL PRIMARY KEY,
  cache_key TEXT,
  category TEXT NOT NULL,
  current_brand TEXT NOT NULL,
  current_rating TEXT NOT NULL,
  alternatives_data JSONB,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_alternative_products_lookup ON public.alternative_products (category, current_brand, current_rating);
CREATE INDEX IF NOT EXISTS idx_alternative_products_cache_key ON public.alternative_products (cache_key);
