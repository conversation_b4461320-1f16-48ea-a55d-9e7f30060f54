{"name": "social-goods", "version": "0.1.0", "description": "Chrome extension for displaying ethical scores of products on Amazon", "main": "index.js", "scripts": {"build": "./build.sh", "build:prod": "./build.sh", "dev": "./build.sh --dev --watch", "dev:local": "./build.sh --dev --watch --skip-copy", "build:local": "./build.sh --skip-copy", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "type-check": "tsc --noEmit", "fix-all": "npm run format && npm run lint && npm run type-check"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@supabase/supabase-js": "^2.49.4", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "firebase": "^11.9.1", "jsonwebtoken": "^9.0.2", "process": "^0.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "stream-browserify": "^3.0.0", "util": "^0.12.5"}, "eslintIgnore": ["node_modules", "dist", "build", "functions/lib"], "devDependencies": {"@types/chrome": "^0.0.254", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.14.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^7.3.1", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "dotenv": "^16.5.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.7.0", "prettier": "^3.2.5", "style-loader": "^3.3.3", "supabase": "^2.22.4", "ts-loader": "^9.5.1", "typescript": "^5.3.2", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}}